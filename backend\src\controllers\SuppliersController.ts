import { Request, Response } from "express";
import { AppDataSource } from "../dbconfig";
import { Suppliers } from "../models/Suppliers.model";

export const getAllSuppliers = async (req: Request, res: Response) => {
    const suppliers = await AppDataSource.getRepository(Suppliers).find();
    res.json(suppliers);
}

export const getSupplier = async (req: Request, res: Response) => {
    try {
        const id = Number(req.params.id);
        const supplierRepository = AppDataSource.getRepository(Suppliers);
        const supplier = await supplierRepository.findOneBy({ id: id });

        if (!supplier) {
            return res.status(404).json({ type: "error", message: "Supplier not found" });
        }

        res.json(supplier);
        return;
    } catch (e) {
        console.error("Error fetching supplier:", e);
        res.status(500).json({ type: "error", message: "Failed to Fetch Supplier" });
        return;
    }
}

export const addSupplier = async (req: Request, res: Response) => {
    try {
        const supplierRepository = AppDataSource.getRepository(Suppliers);
        const newSupplier = supplierRepository.create(req.body);
        await supplierRepository.save(newSupplier);
        res.status(201).json({ type: "success", message: "Supplier added successfully" });
    } catch (e) {
        console.error("Error adding supplier:", e);
        res.status(500).json({ type: "error", message: "Failed to add supplier" });
    }
}

export const editSupplier = async (req: Request, res: Response) => {
    try {
        const id = Number(req.params.id);
        const supplierModel = AppDataSource.getRepository(Suppliers);
        const oldSupplier = await supplierModel.findOne({
            where: {
                id: id
            }
        })
        if (!oldSupplier) {
            res.status(404).json({ type: "error", message: "Supplier Not Found" })
            return;
        }

        await supplierModel.update(
            { id: id },
            {
                name: req.body.name,
                phone: req.body.phone,
                address: req.body.address,
                gstNumber: req.body.gstNumber,
                status: req.body.status
            }
        );
        res.status(200).json({ type: "success", message: "Supplier updated successfully" });
        return;
    } catch (e) {
        console.log("edit suppliers error: ", e);
        res.status(500).json({ type: "error", message: "Failed to edit Suppliers" });
        return;
    }
}

export const deleteSupplier = async (req: Request, res: Response) => {
    try {
        const supplierId = Number(req.params.id);
        const supplierRepository = AppDataSource.getRepository(Suppliers);
        const supplier = await supplierRepository.findOneBy({ id: supplierId });

        if (!supplier) {
            return res.status(404).json({ type: "error", message: "Supplier Not Found" });
        }

        await supplierRepository.delete(supplierId);
        return res.status(200).json({ type: "success", message: "Supplier deleted successfully" });
    } catch (e) {
        console.error("Delete supplier error:", e);
        return res.status(500).json({ type: "error", message: "Failed to delete supplier" });
    }
}

export const toggleSupplierStatus = async (req: Request, res: Response) => {
    try {
        const supplierModel = AppDataSource.getRepository(Suppliers);
        const supplier = await supplierModel.findOneBy({ id: Number(req.params.id) });

        if (!supplier) {
            return res.status(404).json({ type: "error", message: "Supplier not found" });
        }

        const newStatus = supplier.status === 1 ? 0 : 1;

        await supplierModel.update(supplier.id, { status: newStatus });

        return res.status(200).json({
            type: "success",
            message: `Supplier ${newStatus ? 'Activated' : 'Deactivated'} successfully`
        });
    } catch (e) {
        console.error("Status update error:", e);
        return res.status(500).json({ type: "error", message: "Failed to update status" });
    }
}