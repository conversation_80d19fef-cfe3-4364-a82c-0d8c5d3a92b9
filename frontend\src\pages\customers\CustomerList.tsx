import React, { useState, useMemo } from 'react';
import { Link } from 'react-router-dom';
import { 
    Search, 
    Filter, 
    Plus, 
    Eye, 
    Edit, 
    Trash2, 
    MoreHorizontal,
    UserCheck,
    UserX,
    Phone,
    MapPin,
    CreditCard,
    TrendingUp,
    AlertTriangle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
    Table, 
    TableBody, 
    TableCell, 
    TableHead, 
    TableHeader, 
    TableRow 
} from '@/components/ui/table';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { useApi } from '@/hooks/useApi';
import { customerApi, Customer } from '@/services/customerApi';
import { GlobalLoader } from '@/components/common/GlobalLoader';
import { GlobalAlert } from '@/components/common/GlobalAlert';

// Fallback to sample data for development
import { sampleCustomers } from '@/data/sampleCustomers';

interface CustomerFilters {
    search: string;
    category: string;
    creditStatus: string;
    status: string;
}

const CustomerList: React.FC = () => {
    const [filters, setFilters] = useState<CustomerFilters>({
        search: '',
        category: 'all',
        creditStatus: 'all',
        status: 'all'
    });
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSize] = useState(10);
    const [alertState, setAlertState] = useState<{
        show: boolean;
        message: string;
        type: 'success' | 'error' | 'warning';
    }>({ show: false, message: '', type: 'success' });

    // API call with fallback to sample data
    const { 
        data: apiCustomers, 
        loading, 
        error 
    } = useApi(() => customerApi.searchCustomers({
        search: filters.search,
        category: filters.category !== 'all' ? filters.category : undefined,
        page: currentPage,
        limit: pageSize
    }));

    // Use API data if available, otherwise fallback to sample data
    const customers = useMemo(() => {
        if (apiCustomers?.data) {
            return apiCustomers.data;
        }
        
        // Transform sample data to match API interface
        return sampleCustomers.map(customer => ({
            ...customer,
            id: parseInt(customer.id),
            creditUtilization: customer.creditLimit > 0 ? (customer.currentCredit / customer.creditLimit) * 100 : 0,
            creditStatus: (() => {
                const utilization = customer.creditLimit > 0 ? (customer.currentCredit / customer.creditLimit) * 100 : 0;
                if (utilization >= 90) return "Overdue" as const;
                if (utilization >= 70) return "Warning" as const;
                return "Good" as const;
            })(),
            availableCredit: customer.creditLimit - customer.currentCredit,
            isActive: true,
            loyaltyPoints: Math.floor(customer.totalPurchases / 100),
            lastPurchaseDate: customer.lastPurchase,
            createdAt: customer.joinDate,
            updatedAt: customer.joinDate
        }));
    }, [apiCustomers, sampleCustomers]);

    // Filter customers based on current filters
    const filteredCustomers = useMemo(() => {
        return customers.filter(customer => {
            const matchesSearch = !filters.search || 
                customer.name.toLowerCase().includes(filters.search.toLowerCase()) ||
                customer.phone.includes(filters.search) ||
                (customer.address && customer.address.toLowerCase().includes(filters.search.toLowerCase()));
            
            const matchesCategory = filters.category === 'all' || customer.category === filters.category;
            
            const matchesCreditStatus = filters.creditStatus === 'all' || customer.creditStatus === filters.creditStatus;
            
            const matchesStatus = filters.status === 'all' || 
                (filters.status === 'active' && customer.isActive) ||
                (filters.status === 'inactive' && !customer.isActive);

            return matchesSearch && matchesCategory && matchesCreditStatus && matchesStatus;
        });
    }, [customers, filters]);

    // Pagination
    const totalPages = Math.ceil(filteredCustomers.length / pageSize);
    const paginatedCustomers = filteredCustomers.slice(
        (currentPage - 1) * pageSize,
        currentPage * pageSize
    );

    const handleFilterChange = (key: keyof CustomerFilters, value: string) => {
        setFilters(prev => ({ ...prev, [key]: value }));
        setCurrentPage(1); // Reset to first page when filtering
    };

    const showAlert = (message: string, type: 'success' | 'error' | 'warning') => {
        setAlertState({ show: true, message, type });
        setTimeout(() => setAlertState(prev => ({ ...prev, show: false })), 3000);
    };

    const handleToggleStatus = async (customerId: number, currentStatus: boolean) => {
        try {
            // API call would go here
            showAlert(
                `Customer ${currentStatus ? 'deactivated' : 'activated'} successfully`,
                'success'
            );
        } catch (error) {
            showAlert('Failed to update customer status', 'error');
        }
    };

    const handleDeleteCustomer = async (customerId: number) => {
        if (!confirm('Are you sure you want to delete this customer?')) return;
        
        try {
            // API call would go here
            showAlert('Customer deleted successfully', 'success');
        } catch (error) {
            showAlert('Failed to delete customer', 'error');
        }
    };

    const getCreditStatusColor = (status: string) => {
        switch (status) {
            case 'Good': return 'bg-green-100 text-green-800';
            case 'Warning': return 'bg-yellow-100 text-yellow-800';
            case 'Overdue': return 'bg-red-100 text-red-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const getCategoryColor = (category: string) => {
        switch (category) {
            case 'VIP': return 'bg-purple-100 text-purple-800';
            case 'Wholesale': return 'bg-blue-100 text-blue-800';
            case 'Regular': return 'bg-gray-100 text-gray-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    if (loading) return <GlobalLoader />;

    return (
        <div className="space-y-6">
            {/* Alert */}
            {alertState.show && (
                <GlobalAlert 
                    message={alertState.message} 
                    type={alertState.type} 
                />
            )}

            {/* Header */}
            <div className="flex justify-between items-center">
                <div>
                    <h1 className="text-3xl font-bold text-gray-900">Customer Management</h1>
                    <p className="text-gray-600 mt-1">Manage your customers and their information</p>
                </div>
                <Link to="/customers/add">
                    <Button className="flex items-center gap-2">
                        <Plus className="h-4 w-4" />
                        Add Customer
                    </Button>
                </Link>
            </div>

            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm text-gray-600">Total Customers</p>
                                <p className="text-2xl font-bold">{customers.length}</p>
                            </div>
                            <div className="p-2 bg-blue-100 rounded-lg">
                                <UserCheck className="h-6 w-6 text-blue-600" />
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm text-gray-600">Active Customers</p>
                                <p className="text-2xl font-bold">
                                    {customers.filter(c => c.isActive).length}
                                </p>
                            </div>
                            <div className="p-2 bg-green-100 rounded-lg">
                                <TrendingUp className="h-6 w-6 text-green-600" />
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm text-gray-600">Credit Outstanding</p>
                                <p className="text-2xl font-bold">
                                    ₹{customers.reduce((sum, c) => sum + c.currentCredit, 0).toLocaleString()}
                                </p>
                            </div>
                            <div className="p-2 bg-orange-100 rounded-lg">
                                <CreditCard className="h-6 w-6 text-orange-600" />
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm text-gray-600">Overdue Accounts</p>
                                <p className="text-2xl font-bold">
                                    {customers.filter(c => c.creditStatus === 'Overdue').length}
                                </p>
                            </div>
                            <div className="p-2 bg-red-100 rounded-lg">
                                <AlertTriangle className="h-6 w-6 text-red-600" />
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Filters */}
            <Card>
                <CardContent className="p-4">
                    <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                        <div className="relative">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                            <Input
                                placeholder="Search customers..."
                                value={filters.search}
                                onChange={(e) => handleFilterChange('search', e.target.value)}
                                className="pl-10"
                            />
                        </div>

                        <Select value={filters.category} onValueChange={(value) => handleFilterChange('category', value)}>
                            <SelectTrigger>
                                <SelectValue placeholder="Category" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">All Categories</SelectItem>
                                <SelectItem value="Regular">Regular</SelectItem>
                                <SelectItem value="Wholesale">Wholesale</SelectItem>
                                <SelectItem value="VIP">VIP</SelectItem>
                            </SelectContent>
                        </Select>

                        <Select value={filters.creditStatus} onValueChange={(value) => handleFilterChange('creditStatus', value)}>
                            <SelectTrigger>
                                <SelectValue placeholder="Credit Status" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">All Status</SelectItem>
                                <SelectItem value="Good">Good</SelectItem>
                                <SelectItem value="Warning">Warning</SelectItem>
                                <SelectItem value="Overdue">Overdue</SelectItem>
                            </SelectContent>
                        </Select>

                        <Select value={filters.status} onValueChange={(value) => handleFilterChange('status', value)}>
                            <SelectTrigger>
                                <SelectValue placeholder="Status" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">All</SelectItem>
                                <SelectItem value="active">Active</SelectItem>
                                <SelectItem value="inactive">Inactive</SelectItem>
                            </SelectContent>
                        </Select>

                        <Button 
                            variant="outline" 
                            onClick={() => {
                                setFilters({
                                    search: '',
                                    category: 'all',
                                    creditStatus: 'all',
                                    status: 'all'
                                });
                                setCurrentPage(1);
                            }}
                            className="flex items-center gap-2"
                        >
                            <Filter className="h-4 w-4" />
                            Clear Filters
                        </Button>
                    </div>
                </CardContent>
            </Card>

            {/* Customer Table */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                        <span>Customers ({filteredCustomers.length})</span>
                        <div className="text-sm text-gray-500">
                            Page {currentPage} of {totalPages}
                        </div>
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="overflow-x-auto">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Customer</TableHead>
                                    <TableHead>Contact</TableHead>
                                    <TableHead>Category</TableHead>
                                    <TableHead>Credit Info</TableHead>
                                    <TableHead>Total Purchases</TableHead>
                                    <TableHead>Last Purchase</TableHead>
                                    <TableHead>Status</TableHead>
                                    <TableHead className="text-right">Actions</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {paginatedCustomers.length === 0 ? (
                                    <TableRow>
                                        <TableCell colSpan={8} className="text-center py-8">
                                            <div className="flex flex-col items-center gap-2">
                                                <UserX className="h-12 w-12 text-gray-400" />
                                                <p className="text-gray-500">No customers found</p>
                                                <p className="text-sm text-gray-400">
                                                    Try adjusting your search or filters
                                                </p>
                                            </div>
                                        </TableCell>
                                    </TableRow>
                                ) : (
                                    paginatedCustomers.map((customer) => (
                                        <TableRow key={customer.id} className="hover:bg-gray-50">
                                            <TableCell>
                                                <div>
                                                    <div className="font-medium text-gray-900">
                                                        {customer.name}
                                                    </div>
                                                    {customer.businessName && (
                                                        <div className="text-sm text-gray-500">
                                                            {customer.businessName}
                                                        </div>
                                                    )}
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <div className="space-y-1">
                                                    <div className="flex items-center gap-1 text-sm">
                                                        <Phone className="h-3 w-3 text-gray-400" />
                                                        {customer.phone}
                                                    </div>
                                                    {customer.address && (
                                                        <div className="flex items-center gap-1 text-sm text-gray-500">
                                                            <MapPin className="h-3 w-3 text-gray-400" />
                                                            <span className="truncate max-w-32">
                                                                {customer.address}
                                                            </span>
                                                        </div>
                                                    )}
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <Badge className={getCategoryColor(customer.category)}>
                                                    {customer.category}
                                                </Badge>
                                            </TableCell>
                                            <TableCell>
                                                <div className="space-y-1">
                                                    <div className="flex items-center gap-2">
                                                        <Badge className={getCreditStatusColor(customer.creditStatus)}>
                                                            {customer.creditStatus}
                                                        </Badge>
                                                    </div>
                                                    <div className="text-sm text-gray-600">
                                                        ₹{customer.currentCredit.toLocaleString()} / ₹{customer.creditLimit.toLocaleString()}
                                                    </div>
                                                    <div className="w-full bg-gray-200 rounded-full h-1.5">
                                                        <div
                                                            className={`h-1.5 rounded-full ${
                                                                customer.creditUtilization >= 90 ? 'bg-red-500' :
                                                                customer.creditUtilization >= 70 ? 'bg-yellow-500' : 'bg-green-500'
                                                            }`}
                                                            style={{ width: `${Math.min(customer.creditUtilization, 100)}%` }}
                                                        />
                                                    </div>
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <div className="text-sm">
                                                    <div className="font-medium">
                                                        ₹{customer.totalPurchases.toLocaleString()}
                                                    </div>
                                                    {customer.loyaltyPoints > 0 && (
                                                        <div className="text-gray-500">
                                                            {customer.loyaltyPoints} points
                                                        </div>
                                                    )}
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <div className="text-sm text-gray-600">
                                                    {customer.lastPurchaseDate ?
                                                        new Date(customer.lastPurchaseDate).toLocaleDateString('en-IN') :
                                                        'Never'
                                                    }
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <Badge variant={customer.isActive ? "default" : "secondary"}>
                                                    {customer.isActive ? 'Active' : 'Inactive'}
                                                </Badge>
                                            </TableCell>
                                            <TableCell className="text-right">
                                                <DropdownMenu>
                                                    <DropdownMenuTrigger asChild>
                                                        <Button variant="ghost" className="h-8 w-8 p-0">
                                                            <MoreHorizontal className="h-4 w-4" />
                                                        </Button>
                                                    </DropdownMenuTrigger>
                                                    <DropdownMenuContent align="end">
                                                        <DropdownMenuItem asChild>
                                                            <Link to={`/customers/details/${customer.id}`} className="flex items-center gap-2">
                                                                <Eye className="h-4 w-4" />
                                                                View Details
                                                            </Link>
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem asChild>
                                                            <Link to={`/customers/edit/${customer.id}`} className="flex items-center gap-2">
                                                                <Edit className="h-4 w-4" />
                                                                Edit Customer
                                                            </Link>
                                                        </DropdownMenuItem>
                                                        <DropdownMenuSeparator />
                                                        <DropdownMenuItem
                                                            onClick={() => handleToggleStatus(customer.id, customer.isActive)}
                                                            className="flex items-center gap-2"
                                                        >
                                                            {customer.isActive ? (
                                                                <>
                                                                    <UserX className="h-4 w-4" />
                                                                    Deactivate
                                                                </>
                                                            ) : (
                                                                <>
                                                                    <UserCheck className="h-4 w-4" />
                                                                    Activate
                                                                </>
                                                            )}
                                                        </DropdownMenuItem>
                                                        <DropdownMenuSeparator />
                                                        <DropdownMenuItem
                                                            onClick={() => handleDeleteCustomer(customer.id)}
                                                            className="flex items-center gap-2 text-red-600"
                                                        >
                                                            <Trash2 className="h-4 w-4" />
                                                            Delete Customer
                                                        </DropdownMenuItem>
                                                    </DropdownMenuContent>
                                                </DropdownMenu>
                                            </TableCell>
                                        </TableRow>
                                    ))
                                )}
                            </TableBody>
                        </Table>
                    </div>

                    {/* Pagination */}
                    {totalPages > 1 && (
                        <div className="flex items-center justify-between mt-4">
                            <div className="text-sm text-gray-500">
                                Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, filteredCustomers.length)} of {filteredCustomers.length} customers
                            </div>
                            <div className="flex items-center gap-2">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                                    disabled={currentPage === 1}
                                >
                                    Previous
                                </Button>
                                <div className="flex items-center gap-1">
                                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                                        const page = i + 1;
                                        return (
                                            <Button
                                                key={page}
                                                variant={currentPage === page ? "default" : "outline"}
                                                size="sm"
                                                onClick={() => setCurrentPage(page)}
                                                className="w-8 h-8 p-0"
                                            >
                                                {page}
                                            </Button>
                                        );
                                    })}
                                    {totalPages > 5 && (
                                        <>
                                            <span className="text-gray-400">...</span>
                                            <Button
                                                variant={currentPage === totalPages ? "default" : "outline"}
                                                size="sm"
                                                onClick={() => setCurrentPage(totalPages)}
                                                className="w-8 h-8 p-0"
                                            >
                                                {totalPages}
                                            </Button>
                                        </>
                                    )}
                                </div>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                                    disabled={currentPage === totalPages}
                                >
                                    Next
                                </Button>
                            </div>
                        </div>
                    )}
                </CardContent>
            </Card>
        </div>
    );
};

export default CustomerList;
