import { useState, useEffect, useCallback } from 'react';
import { ApiResponse, handleApiError, LoadingState, createLoadingState, setLoading, setError } from '@/lib/api';

// Generic hook for API calls with loading states
export function useApi<T>(
    apiCall: () => Promise<ApiResponse<T>>,
    dependencies: any[] = [],
    immediate: boolean = true
) {
    const [data, setData] = useState<T | null>(null);
    const [loadingState, setLoadingState] = useState<LoadingState>(createLoadingState());

    const execute = useCallback(async () => {
        setLoadingState(prev => setLoading(prev, true));
        
        try {
            const response = await apiCall();
            if (response.success && response.data) {
                setData(response.data);
                setLoadingState(prev => setLoading(prev, false));
            } else {
                throw new Error(response.message || 'API call failed');
            }
        } catch (error) {
            const errorMessage = handleApiError(error);
            setLoadingState(prev => setError(prev, errorMessage));
            setData(null);
        }
    }, dependencies);

    useEffect(() => {
        if (immediate) {
            execute();
        }
    }, [execute, immediate]);

    const refetch = useCallback(() => {
        execute();
    }, [execute]);

    return {
        data,
        isLoading: loadingState.isLoading,
        error: loadingState.error,
        refetch,
        execute
    };
}

// Hook for API calls that don't need immediate execution
export function useApiCall<T, P = any>(
    apiCall: (params?: P) => Promise<ApiResponse<T>>
) {
    const [data, setData] = useState<T | null>(null);
    const [loadingState, setLoadingState] = useState<LoadingState>(createLoadingState());

    const execute = useCallback(async (params?: P) => {
        setLoadingState(prev => setLoading(prev, true));
        
        try {
            const response = await apiCall(params);
            if (response.success && response.data) {
                setData(response.data);
                setLoadingState(prev => setLoading(prev, false));
                return response.data;
            } else {
                throw new Error(response.message || 'API call failed');
            }
        } catch (error) {
            const errorMessage = handleApiError(error);
            setLoadingState(prev => setError(prev, errorMessage));
            setData(null);
            throw error;
        }
    }, [apiCall]);

    const reset = useCallback(() => {
        setData(null);
        setLoadingState(createLoadingState());
    }, []);

    return {
        data,
        isLoading: loadingState.isLoading,
        error: loadingState.error,
        execute,
        reset
    };
}

// Hook for paginated API calls
export function usePaginatedApi<T>(
    apiCall: (params: { page: number; limit: number; [key: string]: any }) => Promise<ApiResponse<T[]>>,
    initialParams: { page?: number; limit?: number; [key: string]: any } = {},
    immediate: boolean = true
) {
    const [data, setData] = useState<T[]>([]);
    const [pagination, setPagination] = useState({
        page: initialParams.page || 1,
        limit: initialParams.limit || 10,
        total: 0
    });
    const [loadingState, setLoadingState] = useState<LoadingState>(createLoadingState());
    const [params, setParams] = useState(initialParams);

    const execute = useCallback(async (newParams?: any) => {
        setLoadingState(prev => setLoading(prev, true));
        
        const requestParams = {
            ...params,
            ...newParams,
            page: newParams?.page || pagination.page,
            limit: newParams?.limit || pagination.limit
        };
        
        try {
            const response = await apiCall(requestParams);
            if (response.success && response.data) {
                setData(response.data);
                if (response.pagination) {
                    setPagination(response.pagination);
                }
                setLoadingState(prev => setLoading(prev, false));
            } else {
                throw new Error(response.message || 'API call failed');
            }
        } catch (error) {
            const errorMessage = handleApiError(error);
            setLoadingState(prev => setError(prev, errorMessage));
            setData([]);
        }
    }, [apiCall, params, pagination.page, pagination.limit]);

    useEffect(() => {
        if (immediate) {
            execute();
        }
    }, []);

    const nextPage = useCallback(() => {
        if (pagination.page * pagination.limit < pagination.total) {
            execute({ page: pagination.page + 1 });
        }
    }, [execute, pagination]);

    const prevPage = useCallback(() => {
        if (pagination.page > 1) {
            execute({ page: pagination.page - 1 });
        }
    }, [execute, pagination]);

    const goToPage = useCallback((page: number) => {
        execute({ page });
    }, [execute]);

    const updateParams = useCallback((newParams: any) => {
        setParams(prev => ({ ...prev, ...newParams }));
        execute({ ...newParams, page: 1 }); // Reset to first page when params change
    }, [execute]);

    const refetch = useCallback(() => {
        execute();
    }, [execute]);

    return {
        data,
        pagination,
        isLoading: loadingState.isLoading,
        error: loadingState.error,
        nextPage,
        prevPage,
        goToPage,
        updateParams,
        refetch,
        hasNextPage: pagination.page * pagination.limit < pagination.total,
        hasPrevPage: pagination.page > 1
    };
}

// Hook for mutations (create, update, delete operations)
export function useMutation<T, P = any>(
    mutationFn: (params: P) => Promise<ApiResponse<T>>,
    options?: {
        onSuccess?: (data: T) => void;
        onError?: (error: string) => void;
    }
) {
    const [data, setData] = useState<T | null>(null);
    const [loadingState, setLoadingState] = useState<LoadingState>(createLoadingState());

    const mutate = useCallback(async (params: P) => {
        setLoadingState(prev => setLoading(prev, true));
        
        try {
            const response = await mutationFn(params);
            if (response.success && response.data) {
                setData(response.data);
                setLoadingState(prev => setLoading(prev, false));
                options?.onSuccess?.(response.data);
                return response.data;
            } else {
                throw new Error(response.message || 'Mutation failed');
            }
        } catch (error) {
            const errorMessage = handleApiError(error);
            setLoadingState(prev => setError(prev, errorMessage));
            options?.onError?.(errorMessage);
            throw error;
        }
    }, [mutationFn, options]);

    const reset = useCallback(() => {
        setData(null);
        setLoadingState(createLoadingState());
    }, []);

    return {
        data,
        isLoading: loadingState.isLoading,
        error: loadingState.error,
        mutate,
        reset
    };
}
