import { Request, Response } from "express";
import { AppDataSource } from "../config/database";
import { ReportingService } from "../services/ReportingService";

export class ReportingController {
    private reportingService: ReportingService;

    constructor() {
        this.reportingService = new ReportingService(AppDataSource);
    }

    // Get comprehensive sales analytics
    getSalesAnalytics = async (req: Request, res: Response): Promise<void> => {
        try {
            const startDate = req.query.startDate ? new Date(req.query.startDate as string) : undefined;
            const endDate = req.query.endDate ? new Date(req.query.endDate as string) : undefined;

            if (!startDate || !endDate) {
                res.status(400).json({
                    success: false,
                    message: "Start date and end date are required"
                });
                return;
            }

            if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
                res.status(400).json({
                    success: false,
                    message: "Invalid date format"
                });
                return;
            }

            if (startDate > endDate) {
                res.status(400).json({
                    success: false,
                    message: "Start date cannot be after end date"
                });
                return;
            }

            const analytics = await this.reportingService.getSalesAnalytics({
                startDate,
                endDate
            });

            res.json({
                success: true,
                data: {
                    period: {
                        startDate: startDate.toISOString().split('T')[0],
                        endDate: endDate.toISOString().split('T')[0]
                    },
                    analytics
                }
            });

        } catch (error) {
            console.error("Error fetching sales analytics:", error);
            res.status(500).json({
                success: false,
                message: "Failed to fetch sales analytics"
            });
        }
    };

    // Get customer analytics
    getCustomerAnalytics = async (req: Request, res: Response): Promise<void> => {
        try {
            const analytics = await this.reportingService.getCustomerAnalytics();

            res.json({
                success: true,
                data: analytics
            });

        } catch (error) {
            console.error("Error fetching customer analytics:", error);
            res.status(500).json({
                success: false,
                message: "Failed to fetch customer analytics"
            });
        }
    };

    // Get product performance report
    getProductPerformance = async (req: Request, res: Response): Promise<void> => {
        try {
            const startDate = req.query.startDate ? new Date(req.query.startDate as string) : undefined;
            const endDate = req.query.endDate ? new Date(req.query.endDate as string) : undefined;

            if (!startDate || !endDate) {
                res.status(400).json({
                    success: false,
                    message: "Start date and end date are required"
                });
                return;
            }

            if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
                res.status(400).json({
                    success: false,
                    message: "Invalid date format"
                });
                return;
            }

            const performance = await this.reportingService.getProductPerformance({
                startDate,
                endDate
            });

            res.json({
                success: true,
                data: {
                    period: {
                        startDate: startDate.toISOString().split('T')[0],
                        endDate: endDate.toISOString().split('T')[0]
                    },
                    performance
                }
            });

        } catch (error) {
            console.error("Error fetching product performance:", error);
            res.status(500).json({
                success: false,
                message: "Failed to fetch product performance"
            });
        }
    };

    // Get dashboard summary (combines multiple reports)
    getDashboardSummary = async (req: Request, res: Response): Promise<void> => {
        try {
            // Default to last 30 days if no date range provided
            const endDate = new Date();
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - 30);

            // Override with query params if provided
            const queryStartDate = req.query.startDate ? new Date(req.query.startDate as string) : startDate;
            const queryEndDate = req.query.endDate ? new Date(req.query.endDate as string) : endDate;

            if (isNaN(queryStartDate.getTime()) || isNaN(queryEndDate.getTime())) {
                res.status(400).json({
                    success: false,
                    message: "Invalid date format"
                });
                return;
            }

            // Get all analytics in parallel
            const [salesAnalytics, customerAnalytics, productPerformance] = await Promise.all([
                this.reportingService.getSalesAnalytics({
                    startDate: queryStartDate,
                    endDate: queryEndDate
                }),
                this.reportingService.getCustomerAnalytics(),
                this.reportingService.getProductPerformance({
                    startDate: queryStartDate,
                    endDate: queryEndDate
                })
            ]);

            res.json({
                success: true,
                data: {
                    period: {
                        startDate: queryStartDate.toISOString().split('T')[0],
                        endDate: queryEndDate.toISOString().split('T')[0]
                    },
                    salesAnalytics,
                    customerAnalytics,
                    productPerformance
                }
            });

        } catch (error) {
            console.error("Error fetching dashboard summary:", error);
            res.status(500).json({
                success: false,
                message: "Failed to fetch dashboard summary"
            });
        }
    };

    // Get monthly comparison report
    getMonthlyComparison = async (req: Request, res: Response): Promise<void> => {
        try {
            const year = parseInt(req.query.year as string) || new Date().getFullYear();
            const month = parseInt(req.query.month as string) || new Date().getMonth() + 1;

            if (month < 1 || month > 12) {
                res.status(400).json({
                    success: false,
                    message: "Month must be between 1 and 12"
                });
                return;
            }

            // Current month
            const currentMonthStart = new Date(year, month - 1, 1);
            const currentMonthEnd = new Date(year, month, 0);

            // Previous month
            const previousMonthStart = new Date(year, month - 2, 1);
            const previousMonthEnd = new Date(year, month - 1, 0);

            // Get analytics for both months
            const [currentMonth, previousMonth] = await Promise.all([
                this.reportingService.getSalesAnalytics({
                    startDate: currentMonthStart,
                    endDate: currentMonthEnd
                }),
                this.reportingService.getSalesAnalytics({
                    startDate: previousMonthStart,
                    endDate: previousMonthEnd
                })
            ]);

            // Calculate growth percentages
            const salesGrowth = previousMonth.totalSales > 0 
                ? ((currentMonth.totalSales - previousMonth.totalSales) / previousMonth.totalSales) * 100 
                : 0;

            const transactionGrowth = previousMonth.totalTransactions > 0 
                ? ((currentMonth.totalTransactions - previousMonth.totalTransactions) / previousMonth.totalTransactions) * 100 
                : 0;

            const aovGrowth = previousMonth.averageOrderValue > 0 
                ? ((currentMonth.averageOrderValue - previousMonth.averageOrderValue) / previousMonth.averageOrderValue) * 100 
                : 0;

            res.json({
                success: true,
                data: {
                    currentMonth: {
                        period: `${year}-${month.toString().padStart(2, '0')}`,
                        analytics: currentMonth
                    },
                    previousMonth: {
                        period: `${year}-${(month - 1).toString().padStart(2, '0')}`,
                        analytics: previousMonth
                    },
                    growth: {
                        salesGrowth: Number(salesGrowth.toFixed(2)),
                        transactionGrowth: Number(transactionGrowth.toFixed(2)),
                        aovGrowth: Number(aovGrowth.toFixed(2))
                    }
                }
            });

        } catch (error) {
            console.error("Error fetching monthly comparison:", error);
            res.status(500).json({
                success: false,
                message: "Failed to fetch monthly comparison"
            });
        }
    };

    // Get yearly summary
    getYearlySummary = async (req: Request, res: Response): Promise<void> => {
        try {
            const year = parseInt(req.query.year as string) || new Date().getFullYear();

            const startDate = new Date(year, 0, 1);
            const endDate = new Date(year, 11, 31);

            const analytics = await this.reportingService.getSalesAnalytics({
                startDate,
                endDate
            });

            res.json({
                success: true,
                data: {
                    year,
                    analytics
                }
            });

        } catch (error) {
            console.error("Error fetching yearly summary:", error);
            res.status(500).json({
                success: false,
                message: "Failed to fetch yearly summary"
            });
        }
    };

    // Export sales report (basic implementation)
    exportSalesReport = async (req: Request, res: Response): Promise<void> => {
        try {
            const startDate = req.query.startDate ? new Date(req.query.startDate as string) : undefined;
            const endDate = req.query.endDate ? new Date(req.query.endDate as string) : undefined;
            const format = req.query.format as string || "json";

            if (!startDate || !endDate) {
                res.status(400).json({
                    success: false,
                    message: "Start date and end date are required"
                });
                return;
            }

            const analytics = await this.reportingService.getSalesAnalytics({
                startDate,
                endDate
            });

            if (format === "csv") {
                // Basic CSV export (can be enhanced)
                const csvData = this.convertToCSV(analytics);
                res.setHeader('Content-Type', 'text/csv');
                res.setHeader('Content-Disposition', `attachment; filename=sales-report-${startDate.toISOString().split('T')[0]}-to-${endDate.toISOString().split('T')[0]}.csv`);
                res.send(csvData);
            } else {
                // JSON export
                res.setHeader('Content-Type', 'application/json');
                res.setHeader('Content-Disposition', `attachment; filename=sales-report-${startDate.toISOString().split('T')[0]}-to-${endDate.toISOString().split('T')[0]}.json`);
                res.json({
                    exportDate: new Date().toISOString(),
                    period: {
                        startDate: startDate.toISOString().split('T')[0],
                        endDate: endDate.toISOString().split('T')[0]
                    },
                    data: analytics
                });
            }

        } catch (error) {
            console.error("Error exporting sales report:", error);
            res.status(500).json({
                success: false,
                message: "Failed to export sales report"
            });
        }
    };

    private convertToCSV(analytics: any): string {
        // Basic CSV conversion - can be enhanced based on requirements
        const headers = ["Metric", "Value"];
        const rows = [
            ["Total Sales", analytics.totalSales],
            ["Total Transactions", analytics.totalTransactions],
            ["Average Order Value", analytics.averageOrderValue],
            ["Total Items Sold", analytics.totalItemsSold],
            ["Total GST Collected", analytics.totalGstCollected]
        ];

        const csvContent = [
            headers.join(","),
            ...rows.map(row => row.join(","))
        ].join("\n");

        return csvContent;
    }
}
