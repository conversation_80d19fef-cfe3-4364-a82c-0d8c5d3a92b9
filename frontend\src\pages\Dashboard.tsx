import { useNavigate } from "react-router-dom"
import {
    ShoppingCart, Package, Users, CreditCard, Plus, BarChart3,
    AlertTriangle, Clock, TrendingUp, FileText, DollarSign, UserPlus,
    Eye, Settings, Truck, RefreshCw
} from "lucide-react"
import { MetricCard } from "@/components/dashboard/MetricCard"
import { QuickActionCard } from "@/components/dashboard/QuickActionCard"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { useApi } from "@/hooks/useApi"
import ReportingApiService from "@/services/reportingApi"
import SalesApiService from "@/services/salesApi"
import CustomerApiService from "@/services/customerApi"

// Fallback to sample data if API fails
import { sampleProducts } from "@/data/sampleProducts"
import { sampleCustomers, getOverdueCustomers } from "@/data/sampleCustomers"
import { todaysSales, getTodaysSalesTotal, getTodaysSalesCount, getTotalPendingUdhaar } from "@/data/sampleTransactions"

export default function Dashboard() {
    const navigate = useNavigate()

    // API calls for dashboard data
    const {
        data: dashboardSummary,
        isLoading: isDashboardLoading,
        error: dashboardError,
        refetch: refetchDashboard
    } = useApi(() => ReportingApiService.getDashboardSummary(), [], true)

    const {
        data: todaysSummary,
        isLoading: isTodayLoading,
        error: todayError
    } = useApi(() => SalesApiService.getTodaysSummary(), [], true)

    const {
        data: customerSummary,
        isLoading: isCustomerLoading,
        error: customerError
    } = useApi(() => CustomerApiService.getCustomerSummary(), [], true)

    // Fallback to sample data if API fails
    const todaysSalesTotal = todaysSummary?.totalSales || getTodaysSalesTotal()
    const todaysSalesCount = todaysSummary?.totalTransactions || getTodaysSalesCount()
    const totalProducts = dashboardSummary?.inventory?.totalProducts || sampleProducts.length
    const lowStockProducts = dashboardSummary?.inventory?.lowStock || sampleProducts.filter(p => p.stock <= p.minStock).length
    const totalCustomers = customerSummary?.totalCustomers || sampleCustomers.length
    const overdueCustomers = customerSummary?.totalCreditOutstanding || getOverdueCustomers().length
    const totalPendingUdhaar = dashboardSummary?.customers?.overdueCredit || getTotalPendingUdhaar()

    // Loading state
    const isLoading = isDashboardLoading || isTodayLoading || isCustomerLoading
    const hasError = dashboardError || todayError || customerError

    // Quick action handlers - simple navigation
    const handleNewSale = () => navigate("/sales/new")
    const handleAddProduct = () => navigate("/inventory/products/add")
    const handleAddPurchase = () => navigate("/purchases/add")
    const handleCollectUdhaar = () => navigate("/udhaar/collect")
    const handleAddCustomer = () => navigate("/customers/add")
    const handleStockAlerts = () => navigate("/inventory/stock")
    const handleViewReports = () => navigate("/reports/business")
    const handleSettings = () => navigate("/settings/shop")

    return (
        <div className="flex flex-1 flex-col gap-4 sm:gap-6 p-4 sm:p-6">
            {/* Header */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                <div>
                    <h1 className="text-2xl sm:text-3xl font-bold">Dashboard</h1>
                    <p className="text-muted-foreground text-sm sm:text-base">
                        Welcome back! Here's what's happening in your shop today.
                    </p>
                </div>
                <div className="flex items-center gap-2">
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={refetchDashboard}
                        disabled={isLoading}
                    >
                        <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                        Refresh
                    </Button>
                </div>
            </div>

            {/* Error State */}
            {hasError && (
                <Card className="border-red-200 bg-red-50">
                    <CardContent className="pt-6">
                        <div className="flex items-center gap-2 text-red-600">
                            <AlertTriangle className="h-4 w-4" />
                            <p className="text-sm">
                                Unable to load dashboard data. Showing sample data instead.
                            </p>
                        </div>
                    </CardContent>
                </Card>
            )}

            {/* Key Metrics Cards */}
            <div className="grid gap-3 sm:gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
                <MetricCard
                    title="Today's Sales"
                    value={`₹${todaysSalesTotal.toLocaleString('en-IN')}`}
                    icon={ShoppingCart}
                    trend={{
                        direction: 'up',
                        percentage: 12,
                        label: 'vs yesterday'
                    }}
                />
                <MetricCard
                    title="Total Products"
                    value={totalProducts.toString()}
                    icon={Package}
                    alert={{
                        message: `${lowStockProducts.length} low stock`,
                        type: 'warning'
                    }}
                />
                <MetricCard
                    title="Active Customers"
                    value={totalCustomers.toString()}
                    icon={Users}
                    trend={{
                        direction: 'up',
                        percentage: 5,
                        label: 'new this week'
                    }}
                />
                <MetricCard
                    title="Pending Udhaar"
                    value={`₹${totalPendingUdhaar.toLocaleString('en-IN')}`}
                    icon={CreditCard}
                    alert={{
                        message: `${overdueCustomers.length} overdue`,
                        type: 'warning'
                    }}
                />
            </div>

            {/* Quick Actions Grid */}
            <div>
                <h2 className="text-lg sm:text-xl font-semibold mb-3 sm:mb-4">Quick Actions</h2>
                <div className="grid gap-3 sm:gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
                    <QuickActionCard
                        title="New Sale/Bill"
                        description="Create new bill with barcode scanning"
                        icon={Plus}
                        color="green"
                        onClick={handleNewSale}
                        disabled={true}
                    />
                    <QuickActionCard
                        title="Add Product"
                        description="Add new product to inventory"
                        icon={Package}
                        color="blue"
                        onClick={handleAddProduct}
                    />
                    <QuickActionCard
                        title="Add Purchase"
                        description="Record new purchase from supplier"
                        icon={Truck}
                        color="purple"
                        onClick={handleAddPurchase}
                    />
                    <QuickActionCard
                        title="Collect Udhaar"
                        description="Receive payment from customers"
                        icon={DollarSign}
                        color="orange"
                        onClick={handleCollectUdhaar}
                        disabled={true}
                    />
                    <QuickActionCard
                        title="Add Customer"
                        description="Register new customer"
                        icon={UserPlus}
                        color="indigo"
                        onClick={handleAddCustomer}
                        disabled={true}
                    />
                    <QuickActionCard
                        title="Stock Alerts"
                        description="Check low stock items"
                        icon={AlertTriangle}
                        color="red"
                        onClick={handleStockAlerts}
                    />
                    <QuickActionCard
                        title="View Reports"
                        description="Business analytics and insights"
                        icon={BarChart3}
                        color="blue"
                        onClick={handleViewReports}
                        disabled={true}
                    />
                    <QuickActionCard
                        title="Settings"
                        description="Configure shop settings"
                        icon={Settings}
                        color="purple"
                        onClick={handleSettings}
                        disabled={true}
                    />
                </div>
            </div>

            {/* Recent Activities and Today's Summary */}
            <div className="grid gap-4 sm:gap-6 lg:grid-cols-3">
                {/* Recent Activities - Takes 2 columns */}
                <div className="lg:col-span-2 space-y-4 sm:space-y-6">
                    {/* Recent Sales */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <FileText className="h-5 w-5" />
                                Recent Sales
                            </CardTitle>
                            <CardDescription>Last 5 transactions</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-2 sm:space-y-3">
                                {todaysSales.slice(0, 5).map((sale, index) => (
                                    <div key={sale.id} className="flex items-center justify-between p-2 sm:p-3 bg-gray-50 rounded-lg">
                                        <div className="min-w-0 flex-1">
                                            <p className="font-medium text-sm sm:text-base truncate">{sale.billNumber}</p>
                                            <p className="text-xs sm:text-sm text-muted-foreground truncate">
                                                {sale.customerName || 'Walk-in Customer'} • {sale.paymentMethod}
                                            </p>
                                        </div>
                                        <div className="text-right ml-2">
                                            <p className="font-semibold text-sm sm:text-base">₹{sale.total.toLocaleString('en-IN')}</p>
                                            <p className="text-xs text-muted-foreground">
                                                {new Date(sale.timestamp).toLocaleTimeString('en-IN', {
                                                    hour: '2-digit',
                                                    minute: '2-digit'
                                                })}
                                            </p>
                                        </div>
                                    </div>
                                ))}
                                {todaysSales.length === 0 && (
                                    <p className="text-center text-muted-foreground py-4">No sales today yet</p>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Stock Alerts */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <AlertTriangle className="h-5 w-5 text-orange-500" />
                                Stock Alerts
                                <Badge variant="destructive">{lowStockProducts.length}</Badge>
                            </CardTitle>
                            <CardDescription>Products running low</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                {lowStockProducts.slice(0, 5).map((product) => (
                                    <div key={product.id} className="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                                        <div>
                                            <p className="font-medium">{product.name}</p>
                                            <p className="text-sm text-muted-foreground">{product.category}</p>
                                        </div>
                                        <div className="text-right">
                                            <p className="font-semibold text-orange-600">
                                                {product.stock} {product.unit} left
                                            </p>
                                            <p className="text-xs text-muted-foreground">
                                                Min: {product.minStock} {product.unit}
                                            </p>
                                        </div>
                                    </div>
                                ))}
                                {lowStockProducts.length === 0 && (
                                    <p className="text-center text-green-600 py-4">All products are well stocked! 🎉</p>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Udhaar Alerts */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Clock className="h-5 w-5 text-red-500" />
                                Udhaar Alerts
                                <Badge variant="destructive">{overdueCustomers.length}</Badge>
                            </CardTitle>
                            <CardDescription>Customers with pending payments</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                {overdueCustomers.slice(0, 5).map((customer) => {
                                    const daysSinceLastPurchase = Math.floor(
                                        (new Date().getTime() - new Date(customer.lastPurchase).getTime()) / (1000 * 60 * 60 * 24)
                                    )
                                    return (
                                        <div key={customer.id} className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                                            <div>
                                                <p className="font-medium">{customer.name}</p>
                                                <p className="text-sm text-muted-foreground">{customer.phone}</p>
                                            </div>
                                            <div className="text-right">
                                                <p className="font-semibold text-red-600">
                                                    ₹{customer.currentCredit.toLocaleString('en-IN')}
                                                </p>
                                                <p className="text-xs text-muted-foreground">
                                                    {daysSinceLastPurchase} days overdue
                                                </p>
                                            </div>
                                        </div>
                                    )
                                })}
                                {overdueCustomers.length === 0 && (
                                    <p className="text-center text-green-600 py-4">No overdue payments! 🎉</p>
                                )}
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Today's Summary - Takes 1 column */}
                <div>
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <TrendingUp className="h-5 w-5" />
                                Today's Summary
                            </CardTitle>
                            <CardDescription>{new Date().toLocaleDateString('en-IN', {
                                weekday: 'long',
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric'
                            })}</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-3">
                                <div className="flex justify-between items-center">
                                    <span className="text-sm text-muted-foreground">Sales</span>
                                    <div className="text-right">
                                        <p className="font-semibold">₹{todaysSalesTotal.toLocaleString('en-IN')}</p>
                                        <p className="text-xs text-muted-foreground">{todaysSalesCount} transactions</p>
                                    </div>
                                </div>
                                <Separator />
                                <div className="flex justify-between items-center">
                                    <span className="text-sm text-muted-foreground">Purchases</span>
                                    <div className="text-right">
                                        <p className="font-semibold">₹8,500</p>
                                        <p className="text-xs text-muted-foreground">3 suppliers</p>
                                    </div>
                                </div>
                                <Separator />
                                <div className="flex justify-between items-center">
                                    <span className="text-sm text-muted-foreground">Udhaar Given</span>
                                    <div className="text-right">
                                        <p className="font-semibold">₹1,200</p>
                                        <p className="text-xs text-muted-foreground">4 customers</p>
                                    </div>
                                </div>
                                <Separator />
                                <div className="flex justify-between items-center">
                                    <span className="text-sm text-muted-foreground">Udhaar Collected</span>
                                    <div className="text-right">
                                        <p className="font-semibold text-green-600">₹3,500</p>
                                        <p className="text-xs text-muted-foreground">7 payments</p>
                                    </div>
                                </div>
                            </div>

                            <Separator />

                            <div className="bg-blue-50 p-4 rounded-lg">
                                <h4 className="font-semibold text-blue-900 mb-2">Quick Tip</h4>
                                <p className="text-sm text-blue-700">
                                    Your sales are up 12% compared to yesterday! Consider restocking
                                    {lowStockProducts.length > 0 ? ` ${lowStockProducts[0].name} and other low stock items.` : ' popular items.'}
                                </p>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </div>
    )
}