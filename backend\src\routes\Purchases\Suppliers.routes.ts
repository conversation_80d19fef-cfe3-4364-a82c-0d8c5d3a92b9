import { Router } from "express";
import {
    getAllSuppliers, getSupplier, addSupplier, editSupplier, deleteSupplier,
    toggleSupplierStatus
} from '../../controllers/SuppliersController'

const router = Router();

router.get("/", getAllSuppliers);
router.get("/:id", getSupplier);
router.post("/", addSupplier);
router.put("/:id", editSupplier);
router.patch("/:id/status", toggleSupplierStatus);
router.delete("/:id", deleteSupplier);


export default router;