import { useState } from "react"
import { useNavigate } from "react-router-dom"
import { 
    Search, 
    Filter, 
    Calendar, 
    Download, 
    Eye, 
    ArrowLeft,
    IndianRupee,
    User,
    CreditCard,
    Smartphone,
    Banknote,
    TrendingUp,
    TrendingDown,
    BarChart3
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { sampleSales, getSalesAnalytics } from "@/data/sampleSales"
import type { Sale } from "@/data/sampleSales"

export default function SalesHistory() {
    const navigate = useNavigate()
    
    // State management
    const [searchQuery, setSearchQuery] = useState("")
    const [dateFilter, setDateFilter] = useState("all")
    const [paymentFilter, setPaymentFilter] = useState("all")
    const [customerFilter, setCustomerFilter] = useState("all")
    const [selectedSale, setSelectedSale] = useState<Sale | null>(null)
    
    // Get analytics
    const analytics = getSalesAnalytics(sampleSales)
    
    // Filter sales based on search and filters
    const filteredSales = sampleSales.filter(sale => {
        const matchesSearch = 
            sale.invoiceNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
            (sale.customer?.name.toLowerCase().includes(searchQuery.toLowerCase()) || false) ||
            (sale.customer?.phone.includes(searchQuery) || false) ||
            sale.summary.grandTotal.toString().includes(searchQuery)
        
        const matchesDate = dateFilter === "all" || 
            (dateFilter === "today" && sale.date === new Date().toISOString().split('T')[0]) ||
            (dateFilter === "yesterday" && sale.date === new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0]) ||
            (dateFilter === "week" && new Date(sale.date) >= new Date(Date.now() - 7 * 24 * 60 * 60 * 1000))
        
        const matchesPayment = paymentFilter === "all" || sale.paymentMethod === paymentFilter
        
        const matchesCustomer = customerFilter === "all" ||
            (customerFilter === "walk-in" && !sale.customer) ||
            (customerFilter === "regular" && sale.customer?.category === "Regular") ||
            (customerFilter === "vip" && sale.customer?.category === "VIP") ||
            (customerFilter === "wholesale" && sale.customer?.category === "Wholesale")
        
        return matchesSearch && matchesDate && matchesPayment && matchesCustomer
    }).sort((a, b) => new Date(b.date + ' ' + b.time).getTime() - new Date(a.date + ' ' + a.time).getTime())
    
    const getPaymentIcon = (method: string) => {
        switch (method) {
            case "Cash": return <Banknote className="h-4 w-4" />
            case "UPI": return <Smartphone className="h-4 w-4" />
            case "Credit": return <CreditCard className="h-4 w-4" />
            default: return <IndianRupee className="h-4 w-4" />
        }
    }
    
    const getPaymentColor = (method: string) => {
        switch (method) {
            case "Cash": return "bg-green-100 text-green-800"
            case "UPI": return "bg-blue-100 text-blue-800"
            case "Credit": return "bg-orange-100 text-orange-800"
            default: return "bg-gray-100 text-gray-800"
        }
    }
    
    const formatDate = (date: string) => {
        return new Date(date).toLocaleDateString('en-IN', {
            day: '2-digit',
            month: 'short',
            year: 'numeric'
        })
    }
    
    const formatTime = (time: string) => {
        return new Date(`2024-01-01 ${time}`).toLocaleTimeString('en-IN', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        })
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                    <Button variant="outline" size="sm" onClick={() => navigate("/sales")}>
                        <ArrowLeft className="h-4 w-4 mr-2" />
                        Back to Sales
                    </Button>
                    <div>
                        <h1 className="text-2xl font-bold">Sales History</h1>
                        <p className="text-gray-600">View and manage all sales transactions</p>
                    </div>
                </div>
                <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm">
                        <Download className="h-4 w-4 mr-2" />
                        Export
                    </Button>
                    <Button variant="outline" size="sm">
                        <BarChart3 className="h-4 w-4 mr-2" />
                        Analytics
                    </Button>
                </div>
            </div>

            {/* Analytics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm text-gray-600">Today's Sales</p>
                                <p className="text-2xl font-bold">₹{analytics.todayTotal.toLocaleString('en-IN')}</p>
                                <p className="text-sm text-gray-500">{analytics.todaySales} transactions</p>
                            </div>
                            <div className="p-2 bg-green-100 rounded-lg">
                                <TrendingUp className="h-6 w-6 text-green-600" />
                            </div>
                        </div>
                    </CardContent>
                </Card>
                
                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm text-gray-600">Yesterday's Sales</p>
                                <p className="text-2xl font-bold">₹{analytics.yesterdayTotal.toLocaleString('en-IN')}</p>
                                <p className="text-sm text-gray-500">{analytics.yesterdaySales} transactions</p>
                            </div>
                            <div className="p-2 bg-blue-100 rounded-lg">
                                <TrendingDown className="h-6 w-6 text-blue-600" />
                            </div>
                        </div>
                    </CardContent>
                </Card>
                
                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm text-gray-600">Total Sales</p>
                                <p className="text-2xl font-bold">₹{analytics.totalSales.toLocaleString('en-IN')}</p>
                                <p className="text-sm text-gray-500">{analytics.totalTransactions} transactions</p>
                            </div>
                            <div className="p-2 bg-purple-100 rounded-lg">
                                <IndianRupee className="h-6 w-6 text-purple-600" />
                            </div>
                        </div>
                    </CardContent>
                </Card>
                
                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm text-gray-600">Avg Order Value</p>
                                <p className="text-2xl font-bold">₹{analytics.averageOrderValue.toLocaleString('en-IN')}</p>
                                <p className="text-sm text-gray-500">per transaction</p>
                            </div>
                            <div className="p-2 bg-orange-100 rounded-lg">
                                <BarChart3 className="h-6 w-6 text-orange-600" />
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Search and Filters */}
            <Card>
                <CardHeader>
                    <CardTitle className="text-lg">Search & Filter</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                        <div className="relative">
                            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                            <Input
                                placeholder="Search by invoice, customer, phone..."
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                                className="pl-10"
                            />
                        </div>
                        
                        <Select value={dateFilter} onValueChange={setDateFilter}>
                            <SelectTrigger>
                                <Calendar className="h-4 w-4 mr-2" />
                                <SelectValue placeholder="Date Range" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">All Dates</SelectItem>
                                <SelectItem value="today">Today</SelectItem>
                                <SelectItem value="yesterday">Yesterday</SelectItem>
                                <SelectItem value="week">This Week</SelectItem>
                            </SelectContent>
                        </Select>
                        
                        <Select value={paymentFilter} onValueChange={setPaymentFilter}>
                            <SelectTrigger>
                                <Filter className="h-4 w-4 mr-2" />
                                <SelectValue placeholder="Payment Method" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">All Payments</SelectItem>
                                <SelectItem value="Cash">Cash</SelectItem>
                                <SelectItem value="UPI">UPI</SelectItem>
                                <SelectItem value="Credit">Credit</SelectItem>
                            </SelectContent>
                        </Select>
                        
                        <Select value={customerFilter} onValueChange={setCustomerFilter}>
                            <SelectTrigger>
                                <User className="h-4 w-4 mr-2" />
                                <SelectValue placeholder="Customer Type" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">All Customers</SelectItem>
                                <SelectItem value="walk-in">Walk-in</SelectItem>
                                <SelectItem value="regular">Regular</SelectItem>
                                <SelectItem value="vip">VIP</SelectItem>
                                <SelectItem value="wholesale">Wholesale</SelectItem>
                            </SelectContent>
                        </Select>
                        
                        <Button 
                            variant="outline" 
                            onClick={() => {
                                setSearchQuery("")
                                setDateFilter("all")
                                setPaymentFilter("all")
                                setCustomerFilter("all")
                            }}
                        >
                            Clear Filters
                        </Button>
                    </div>
                </CardContent>
            </Card>

            {/* Sales List */}
            <Card>
                <CardHeader>
                    <div className="flex items-center justify-between">
                        <CardTitle>Sales Transactions ({filteredSales.length})</CardTitle>
                        <CardDescription>
                            Total: ₹{filteredSales.reduce((sum, sale) => sum + sale.summary.grandTotal, 0).toLocaleString('en-IN')}
                        </CardDescription>
                    </div>
                </CardHeader>
                <CardContent>
                    <div className="space-y-4">
                        {filteredSales.length === 0 ? (
                            <div className="text-center py-8 text-gray-500">
                                <p>No sales found matching your criteria</p>
                            </div>
                        ) : (
                            filteredSales.map((sale) => (
                                <div key={sale.id} className="border rounded-lg p-3 sm:p-4 hover:bg-gray-50 transition-colors">
                                    <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
                                        <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
                                            <div className="min-w-0">
                                                <p className="font-medium text-sm sm:text-base">{sale.invoiceNumber}</p>
                                                <p className="text-xs sm:text-sm text-gray-600">
                                                    {formatDate(sale.date)} • {formatTime(sale.time)}
                                                </p>
                                            </div>

                                            <div className="min-w-0">
                                                <p className="font-medium text-sm sm:text-base truncate">
                                                    {sale.customer ? sale.customer.name : "Walk-in Customer"}
                                                </p>
                                                <p className="text-xs sm:text-sm text-gray-600 truncate">
                                                    {sale.customer ? sale.customer.phone : "Cash/UPI Sale"}
                                                </p>
                                            </div>
                                        </div>

                                        {/* Badges - Mobile stacked */}
                                        <div className="flex flex-wrap items-center gap-1 sm:gap-2">
                                            <Badge className={`${getPaymentColor(sale.paymentMethod)} text-xs`}>
                                                {getPaymentIcon(sale.paymentMethod)}
                                                <span className="ml-1">{sale.paymentMethod}</span>
                                            </Badge>
                                            {sale.customer && (
                                                <Badge variant={sale.customer.category === "VIP" ? "default" : sale.customer.category === "Wholesale" ? "secondary" : "outline"} className="text-xs">
                                                    {sale.customer.category}
                                                </Badge>
                                            )}
                                        </div>

                                        {/* Amount and Action - Mobile optimized */}
                                        <div className="flex items-center justify-between sm:gap-4">
                                            <div className="text-left sm:text-right">
                                                <p className="font-bold text-base sm:text-lg">₹{sale.summary.grandTotal.toFixed(2)}</p>
                                                <p className="text-xs sm:text-sm text-gray-600">{sale.summary.itemCount} items</p>
                                            </div>

                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => setSelectedSale(sale)}
                                                className="h-8 px-3 text-xs sm:h-9 sm:px-4 sm:text-sm"
                                            >
                                                <Eye className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                                                <span className="hidden xs:inline">View</span>
                                                <span className="xs:hidden">👁</span>
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            ))
                        )}
                    </div>
                </CardContent>
            </Card>

            {/* Sale Detail Modal - Mobile optimized */}
            <Dialog open={!!selectedSale} onOpenChange={() => setSelectedSale(null)}>
                <DialogContent className="max-w-[95vw] sm:max-w-4xl max-h-[90vh] overflow-y-auto p-3 sm:p-6">
                    <DialogHeader className="pb-3 sm:pb-6">
                        <DialogTitle className="text-lg sm:text-xl">Sale Details - {selectedSale?.invoiceNumber}</DialogTitle>
                    </DialogHeader>

                    {selectedSale && (
                        <div className="space-y-6">
                            {/* Sale Header */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="text-lg">Transaction Info</CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-3">
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">Invoice Number:</span>
                                            <span className="font-medium">{selectedSale.invoiceNumber}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">Date & Time:</span>
                                            <span className="font-medium">
                                                {formatDate(selectedSale.date)} • {formatTime(selectedSale.time)}
                                            </span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">Payment Method:</span>
                                            <Badge className={getPaymentColor(selectedSale.paymentMethod)}>
                                                {getPaymentIcon(selectedSale.paymentMethod)}
                                                <span className="ml-1">{selectedSale.paymentMethod}</span>
                                            </Badge>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">Status:</span>
                                            <Badge variant="default">{selectedSale.status}</Badge>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">Created By:</span>
                                            <span className="font-medium">{selectedSale.createdBy}</span>
                                        </div>
                                    </CardContent>
                                </Card>

                                <Card>
                                    <CardHeader>
                                        <CardTitle className="text-lg">Customer Info</CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-3">
                                        {selectedSale.customer ? (
                                            <>
                                                <div className="flex justify-between">
                                                    <span className="text-gray-600">Name:</span>
                                                    <span className="font-medium">{selectedSale.customer.name}</span>
                                                </div>
                                                <div className="flex justify-between">
                                                    <span className="text-gray-600">Phone:</span>
                                                    <span className="font-medium">{selectedSale.customer.phone}</span>
                                                </div>
                                                <div className="flex justify-between">
                                                    <span className="text-gray-600">Category:</span>
                                                    <Badge variant={selectedSale.customer.category === "VIP" ? "default" : selectedSale.customer.category === "Wholesale" ? "secondary" : "outline"}>
                                                        {selectedSale.customer.category}
                                                    </Badge>
                                                </div>
                                            </>
                                        ) : (
                                            <div className="text-center py-4 text-gray-500">
                                                <User className="h-8 w-8 mx-auto mb-2 opacity-50" />
                                                <p>Walk-in Customer</p>
                                                <p className="text-sm">No customer information</p>
                                            </div>
                                        )}
                                    </CardContent>
                                </Card>
                            </div>

                            {/* Items List */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="text-lg">Items Purchased</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3">
                                        {selectedSale.items.map((item, index) => (
                                            <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                                                <div className="flex-1">
                                                    <p className="font-medium">{item.name}</p>
                                                    <p className="text-sm text-gray-600">{item.category} • GST: {item.gstRate}%</p>
                                                </div>
                                                <div className="text-center">
                                                    <p className="font-medium">{item.quantity} {item.unit}</p>
                                                    <p className="text-sm text-gray-600">@ ₹{item.price}</p>
                                                </div>
                                                <div className="text-right">
                                                    <p className="font-bold">₹{item.total.toFixed(2)}</p>
                                                    <p className="text-sm text-gray-600">GST: ₹{item.gstAmount.toFixed(2)}</p>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Bill Summary */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="text-lg">Bill Summary</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3">
                                        <div className="flex justify-between">
                                            <span>Subtotal ({selectedSale.summary.itemCount} items):</span>
                                            <span>₹{selectedSale.summary.subtotal.toFixed(2)}</span>
                                        </div>

                                        {/* GST Breakdown */}
                                        {selectedSale.summary.gstBreakdown.map((gst, index) => (
                                            <div key={index} className="flex justify-between text-sm text-gray-600">
                                                <span>GST {gst.rate}% on ₹{gst.taxableAmount.toFixed(2)}:</span>
                                                <span>₹{gst.gstAmount.toFixed(2)}</span>
                                            </div>
                                        ))}

                                        <Separator />
                                        <div className="flex justify-between text-lg font-bold">
                                            <span>Grand Total:</span>
                                            <span>₹{selectedSale.summary.grandTotal.toFixed(2)}</span>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    )}
                </DialogContent>
            </Dialog>
        </div>
    )
}
