import { apiClient, ApiResponse } from '@/lib/api';

// Types matching backend interfaces
export interface CreateCustomerRequest {
    name: string;
    phone: string;
    email?: string;
    address?: string;
    category?: "Regular" | "Wholesale" | "VIP";
    creditLimit?: number;
    gstNumber?: string;
    businessName?: string;
    notes?: string;
}

export interface UpdateCustomerRequest extends Partial<CreateCustomerRequest> {
    id: number;
}

export interface Customer {
    id: number;
    name: string;
    phone: string;
    email?: string;
    address?: string;
    category: "Regular" | "Wholesale" | "VIP";
    creditLimit: number;
    currentCredit: number;
    totalPurchases: number;
    loyaltyPoints: number;
    gstNumber?: string;
    businessName?: string;
    notes?: string;
    isActive: boolean;
    lastPurchaseDate?: string;
    createdAt: string;
    updatedAt: string;
    // Virtual properties
    creditUtilization: number;
    creditStatus: "Good" | "Warning" | "Overdue";
    availableCredit: number;
}

export interface CreditTransaction {
    id: number;
    customerId: number;
    saleId?: number;
    type: "Credit_Given" | "Payment_Received" | "Credit_Adjustment";
    amount: number;
    description?: string;
    createdAt: string;
    updatedAt: string;
}

export interface CustomerSummary {
    totalCustomers: number;
    activeCustomers: number;
    newCustomersThisMonth: number;
    totalCreditOutstanding: number;
    averageCreditUtilization: number;
    topCustomersByPurchases: Array<{
        customerId: number;
        customerName: string;
        totalPurchases: number;
        lastPurchaseDate: string;
    }>;
    categoryBreakdown: Array<{
        category: string;
        count: number;
        percentage: number;
    }>;
}

// Customer API service class
export class CustomerApiService {
    // Create a new customer
    static async createCustomer(customerData: CreateCustomerRequest): Promise<ApiResponse<Customer>> {
        return apiClient.post<Customer>('/customers', customerData);
    }

    // Get all customers with optional filters
    static async getCustomers(params?: {
        page?: number;
        limit?: number;
        search?: string;
        category?: string;
        creditStatus?: string;
        isActive?: boolean;
        sortBy?: string;
        sortOrder?: 'ASC' | 'DESC';
    }): Promise<ApiResponse<Customer[]>> {
        return apiClient.get<Customer[]>('/customers', params);
    }

    // Get a specific customer by ID
    static async getCustomerById(customerId: number): Promise<ApiResponse<Customer>> {
        return apiClient.get<Customer>(`/customers/${customerId}`);
    }

    // Update customer information
    static async updateCustomer(customerId: number, customerData: Partial<CreateCustomerRequest>): Promise<ApiResponse<Customer>> {
        return apiClient.put<Customer>(`/customers/${customerId}`, customerData);
    }

    // Delete/deactivate customer
    static async deleteCustomer(customerId: number): Promise<ApiResponse<{ message: string }>> {
        return apiClient.delete(`/customers/${customerId}`);
    }

    // Search customers by name or phone
    static async searchCustomers(query: string): Promise<ApiResponse<Customer[]>> {
        return apiClient.get<Customer[]>('/customers/search', { q: query });
    }

    // Get customer's purchase history
    static async getCustomerPurchaseHistory(customerId: number, params?: {
        page?: number;
        limit?: number;
        startDate?: string;
        endDate?: string;
    }): Promise<ApiResponse<any[]>> {
        return apiClient.get(`/customers/${customerId}/purchases`, params);
    }

    // Get customer's credit transactions
    static async getCustomerCreditHistory(customerId: number, params?: {
        page?: number;
        limit?: number;
        type?: string;
    }): Promise<ApiResponse<CreditTransaction[]>> {
        return apiClient.get<CreditTransaction[]>(`/customers/${customerId}/credit-history`, params);
    }

    // Add credit transaction (payment received, credit adjustment, etc.)
    static async addCreditTransaction(customerId: number, transactionData: {
        type: "Payment_Received" | "Credit_Adjustment";
        amount: number;
        description?: string;
    }): Promise<ApiResponse<CreditTransaction>> {
        return apiClient.post<CreditTransaction>(`/customers/${customerId}/credit-transaction`, transactionData);
    }

    // Get customers with overdue credit
    static async getOverdueCustomers(): Promise<ApiResponse<Customer[]>> {
        return apiClient.get<Customer[]>('/customers/overdue');
    }

    // Get customer summary/analytics
    static async getCustomerSummary(): Promise<ApiResponse<CustomerSummary>> {
        return apiClient.get<CustomerSummary>('/customers/summary');
    }

    // Update customer credit limit
    static async updateCreditLimit(customerId: number, creditLimit: number): Promise<ApiResponse<Customer>> {
        return apiClient.patch<Customer>(`/customers/${customerId}/credit-limit`, { creditLimit });
    }

    // Get customer analytics for a specific customer
    static async getCustomerAnalytics(customerId: number, params?: {
        period?: 'month' | 'quarter' | 'year';
        startDate?: string;
        endDate?: string;
    }): Promise<ApiResponse<{
        totalPurchases: number;
        totalAmount: number;
        averageOrderValue: number;
        purchaseFrequency: number;
        creditUtilization: number;
        loyaltyPoints: number;
        monthlyPurchases: Array<{
            month: string;
            amount: number;
            transactions: number;
        }>;
        topProducts: Array<{
            productId: number;
            productName: string;
            quantity: number;
            amount: number;
        }>;
    }>> {
        return apiClient.get(`/customers/${customerId}/analytics`, params);
    }

    // Export customer data
    static async exportCustomers(params?: {
        format?: 'csv' | 'excel';
        category?: string;
        creditStatus?: string;
        isActive?: boolean;
    }): Promise<ApiResponse<{ downloadUrl: string }>> {
        return apiClient.get('/customers/export', params);
    }

    // Bulk update customers
    static async bulkUpdateCustomers(updates: Array<{
        id: number;
        data: Partial<CreateCustomerRequest>;
    }>): Promise<ApiResponse<{ updated: number; failed: number }>> {
        return apiClient.post('/customers/bulk-update', { updates });
    }
}

// Export default instance for convenience
export default CustomerApiService;
