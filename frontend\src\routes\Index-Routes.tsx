import { createBrowserRouter } from "react-router-dom";
import { lazy } from "react";
const AppLayout = lazy(() => import("@/layout/AppLayout"));
const Dashboard = lazy(() => import("@/pages/Dashboard"));
const Inventory = lazy(() => import("@/pages/inventory/Inventory"));
const AddProduct = lazy(() => import("@/pages/inventory/products/AddProduct"));
const EditProduct = lazy(() => import("@/pages/inventory/products/EditProduct"));
const ProductDetails = lazy(() => import("@/pages/inventory/products/ProductDetails"));
const ProductList = lazy(() => import("@/pages/inventory/products/ProductList"));
const CategoryManagement = lazy(() => import("@/pages/inventory/categories/CategoryManagement"));
const UnitManagement = lazy(() => import("@/pages/inventory/units/UnitManagement"));
const StockManagement = lazy(() => import("@/pages/inventory/stock/StockManagement"));
const StockAdjustment = lazy(() => import("@/pages/inventory/stock/StockAdjustment"));
const StockLogs = lazy(() => import("@/pages/inventory/stock/StockLogs"));
const ImportExport = lazy(() => import("@/pages/inventory/import-export/ImportExport"));
const AlertDemo = lazy(() => import("@/components/common/AlertDemo"));
// Purchase Management Components
const Purchases = lazy(() => import("@/pages/purchases/Purchases"));
const PurchaseList = lazy(() => import("@/pages/purchases/PurchaseList"));
const AddPurchase = lazy(() => import("@/pages/purchases/AddPurchase"));
const EditPurchase = lazy(() => import("@/pages/purchases/EditPurchase"));
const PurchaseDetails = lazy(() => import("@/pages/purchases/PurchaseDetails"));
const SupplierManagement = lazy(() => import("@/pages/purchases/suppliers/SupplierManagement"));
const PurchaseReports = lazy(() => import("@/pages/purchases/reports/PurchaseReports"));
const Sales = lazy(() => import("@/pages/Sales"));
const NewSale = lazy(() => import("@/pages/sales/NewSale"));
const SalesHistory = lazy(() => import("@/pages/sales/SalesHistory"));
const DailySummary = lazy(() => import("@/pages/sales/DailySummary"));
const Settings = lazy(() => import("@/pages/Settings"));
const NotFound = lazy(() => import("@/pages/NotFound"));
const ErrorBoundary = lazy(() => import("@/components/ErrorBoundary"));
const ComingSoon = lazy(() => import("@/components/ComingSoon"));

export const router = createBrowserRouter([
    {
        path: "/",
        element: <AppLayout />,
        errorElement: <ErrorBoundary />,
        children: [
            {
                index: true,
                element: <Dashboard />,
            },
            {
                path: "inventory",
                element: <Inventory />,
            },
            // Product Management Routes
            {
                path: "inventory/products",
                element: <ProductList />,
            },
            {
                path: "inventory/products/add",
                element: <AddProduct />,
            },
            {
                path: "inventory/products/edit/:id",
                element: <EditProduct />,
            },
            {
                path: "inventory/products/details/:id",
                element: <ProductDetails />,
            },
            // Category & Unit Management Routes
            {
                path: "inventory/categories",
                element: <CategoryManagement />,
            },
            {
                path: "inventory/units",
                element: <UnitManagement />,
            },
            // Stock Management Routes
            {
                path: "inventory/stock",
                element: <StockManagement />,
            },
            {
                path: "inventory/stock/adjust",
                element: <StockAdjustment />,
            },
            {
                path: "inventory/stock/logs",
                element: <StockLogs />,
            },
            // Import/Export Routes
            {
                path: "inventory/import-export",
                element: <ImportExport />,
            },
            // Purchase Management Routes
            {
                path: "purchases",
                element: <Purchases />,
            },
            {
                path: "purchases/list",
                element: <PurchaseList />,
            },
            {
                path: "purchases/add",
                element: <AddPurchase />,
            },
            {
                path: "purchases/edit/:id",
                element: <EditPurchase />,
            },
            {
                path: "purchases/details/:id",
                element: <PurchaseDetails />,
            },
            {
                path: "purchases/suppliers",
                element: <SupplierManagement />,
            },
            {
                path: "purchases/reports",
                element: <PurchaseReports />,
            },
            {
                path: "sales",
                element: <Sales />,
            },
            // Sales & Billing Routes
            {
                path: "sales/new",
                element: <NewSale />,
            },
            {
                path: "sales/history",
                element: <SalesHistory />,
            },
            {
                path: "sales/daily",
                element: <DailySummary />,
            },
            {
                path: "sales/reports",
                element: <ComingSoon module="Sales Reports" expectedPhase="Phase 2" description="Comprehensive sales analytics and reporting." />,
            },
            // Customer Routes (Placeholders for Phase 3)
            {
                path: "customers",
                element: <ComingSoon module="Customer Management" expectedPhase="Phase 3" description="Integrated customer management with purchase history and credit tracking." />,
            },
            {
                path: "customers/list",
                element: <ComingSoon module="Customer List" expectedPhase="Phase 3" description="View and manage all customers with search and filtering." />,
            },
            {
                path: "customers/add",
                element: <ComingSoon module="Add Customer" expectedPhase="Phase 3" description="Register new customers with credit limits and categories." />,
            },
            {
                path: "customers/history",
                element: <ComingSoon module="Purchase History" expectedPhase="Phase 3" description="View customer purchase history and analytics." />,
            },
            // Udhaar Routes (Placeholders for Phase 4)
            {
                path: "udhaar/overview",
                element: <ComingSoon module="Udhaar Overview" expectedPhase="Phase 4" description="Complete overview of all credit transactions and pending payments." />,
            },
            {
                path: "udhaar/give",
                element: <ComingSoon module="Give Credit" expectedPhase="Phase 4" description="Issue credit to customers with automatic limit checking." />,
            },
            {
                path: "udhaar/collect",
                element: <ComingSoon module="Collect Payment" expectedPhase="Phase 4" description="Record credit payments with partial payment support." />,
            },
            {
                path: "udhaar/reminders",
                element: <ComingSoon module="Payment Reminders" expectedPhase="Phase 4" description="Manage payment reminders and overdue alerts." />,
            },
            // Enhanced Reports Routes (Placeholders for Phase 5)
            {
                path: "reports",
                element: <ComingSoon module="Reports & Analytics" expectedPhase="Phase 5" description="Comprehensive business reporting and analytics dashboard." />,
            },
            {
                path: "reports/business",
                element: <ComingSoon module="Business Overview" expectedPhase="Phase 5" description="Complete business performance overview with key metrics." />,
            },
            {
                path: "reports/stock",
                element: <ComingSoon module="Stock Reports" expectedPhase="Phase 5" description="Detailed stock analysis and inventory reports." />,
            },
            {
                path: "reports/sales",
                element: <ComingSoon module="Sales Analytics" expectedPhase="Phase 5" description="Advanced sales analytics with trends and forecasting." />,
            },
            {
                path: "reports/udhaar",
                element: <ComingSoon module="Udhaar Reports" expectedPhase="Phase 5" description="Credit management reports and customer payment analytics." />,
            },
            // Enhanced Settings Routes
            {
                path: "settings",
                element: <Settings />,
            },
            {
                path: "settings/shop",
                element: <ComingSoon module="Shop Settings" expectedPhase="Phase 5" description="Configure shop details, GST settings, and business information." />,
            },
            {
                path: "settings/payments",
                element: <ComingSoon module="Payment Methods" expectedPhase="Phase 5" description="Configure payment methods and UPI settings." />,
            },
            {
                path: "settings/users",
                element: <ComingSoon module="User Management" expectedPhase="Phase 5" description="Manage user accounts and permissions." />,
            },
            {
                path: "settings/system",
                element: <ComingSoon module="System Config" expectedPhase="Phase 5" description="System configuration and advanced settings." />,
            },
            {
                path: "alerts-demo",
                element: <AlertDemo />,
            },
            {
                path: "*",
                element: <NotFound />,
            }
        ]
    }
])