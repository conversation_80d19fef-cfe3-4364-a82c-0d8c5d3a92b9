import { Router } from "express";
import productsRoutes from "./products/products.routes";
import suppliersRoutes from "./Purchases/Suppliers.routes";
import helpersRoutes from "./helper.routes";
import salesRoutes from "./salesRoutes";
import customerRoutes from "./customerRoutes";
import reportingRoutes from "./reportingRoutes";

const router = Router();

router.use("/products", productsRoutes);
router.use("/suppliers", suppliersRoutes);
router.use("/helpers", helpersRoutes);
router.use("/sales", salesRoutes);
router.use("/customers", customerRoutes);
router.use("/reports", reportingRoutes);

export default router;