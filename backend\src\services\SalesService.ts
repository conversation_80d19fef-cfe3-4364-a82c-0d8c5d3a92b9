import { Repository, DataSource, QueryRunner } from "typeorm";
import { Sales } from "../models/Sales.model";
import { SaleItem } from "../models/SaleItem.model";
import { Customer } from "../models/Customer.model";
import { Product } from "../models/Product.model";
import { GstBreakdown } from "../models/GstBreakdown.model";
import { PaymentTransaction } from "../models/PaymentTransaction.model";
import { CreditTransaction } from "../models/CreditTransaction.model";
import { StockMovement } from "../models/StockMovement.model";
import { InvoiceSequence } from "../models/InvoiceSequence.model";
import { DailySalesSummary } from "../models/DailySalesSummary.model";

export interface CreateSaleRequest {
    customerId?: number;
    customerName?: string;
    customerPhone?: string;
    paymentMethod: "Cash" | "UPI" | "Credit" | "Card";
    items: Array<{
        productId: number;
        quantity: number;
        unitPrice: number;
        gstRate: number;
        discountAmount?: number;
    }>;
    discountAmount?: number;
    discountPercentage?: number;
    notes?: string;
    paymentDetails?: {
        transactionId?: string;
        upiId?: string;
        referenceNumber?: string;
    };
}

export interface SalesSummary {
    totalSales: number;
    totalTransactions: number;
    averageOrderValue: number;
    paymentMethodBreakdown: Array<{
        method: string;
        amount: number;
        percentage: number;
    }>;
    gstBreakdown: Array<{
        rate: number;
        taxableAmount: number;
        gstAmount: number;
    }>;
}

export class SalesService {
    private salesRepository: Repository<Sales>;
    private saleItemRepository: Repository<SaleItem>;
    private customerRepository: Repository<Customer>;
    private productRepository: Repository<Product>;
    private gstBreakdownRepository: Repository<GstBreakdown>;
    private paymentTransactionRepository: Repository<PaymentTransaction>;
    private creditTransactionRepository: Repository<CreditTransaction>;
    private stockMovementRepository: Repository<StockMovement>;
    private invoiceSequenceRepository: Repository<InvoiceSequence>;
    private dailySalesSummaryRepository: Repository<DailySalesSummary>;
    private dataSource: DataSource;

    constructor(dataSource: DataSource) {
        this.dataSource = dataSource;
        this.salesRepository = dataSource.getRepository(Sales);
        this.saleItemRepository = dataSource.getRepository(SaleItem);
        this.customerRepository = dataSource.getRepository(Customer);
        this.productRepository = dataSource.getRepository(Product);
        this.gstBreakdownRepository = dataSource.getRepository(GstBreakdown);
        this.paymentTransactionRepository = dataSource.getRepository(PaymentTransaction);
        this.creditTransactionRepository = dataSource.getRepository(CreditTransaction);
        this.stockMovementRepository = dataSource.getRepository(StockMovement);
        this.invoiceSequenceRepository = dataSource.getRepository(InvoiceSequence);
        this.dailySalesSummaryRepository = dataSource.getRepository(DailySalesSummary);
    }

    async createSale(saleData: CreateSaleRequest): Promise<Sales> {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();

        try {
            // 1. Validate customer if provided
            let customer: Customer | undefined;
            if (saleData.customerId) {
                customer = await queryRunner.manager.findOne(Customer, {
                    where: { id: saleData.customerId, isActive: true }
                });
                if (!customer) {
                    throw new Error("Customer not found or inactive");
                }
            }

            // 2. Validate products and check stock
            const products = await this.validateProductsAndStock(queryRunner, saleData.items);

            // 3. Generate invoice number
            const invoiceNumber = await this.generateInvoiceNumber(queryRunner);
            const billNumber = await this.generateBillNumber(queryRunner);

            // 4. Calculate totals
            const calculations = this.calculateSaleTotals(saleData.items, saleData.discountAmount, saleData.discountPercentage);

            // 5. Create sale record
            const sale = new Sales();
            sale.invoiceNumber = invoiceNumber;
            sale.billNumber = billNumber;
            sale.customer = customer;
            sale.customerName = saleData.customerName;
            sale.customerPhone = saleData.customerPhone;
            sale.saleDate = new Date();
            sale.saleTime = new Date().toTimeString().split(' ')[0];
            sale.paymentMethod = saleData.paymentMethod;
            sale.paymentStatus = saleData.paymentMethod === "Credit" ? "Pending" : "Paid";
            sale.subtotal = calculations.subtotal;
            sale.totalGst = calculations.totalGst;
            sale.discountAmount = calculations.discountAmount;
            sale.discountPercentage = calculations.discountPercentage;
            sale.grandTotal = calculations.grandTotal;
            sale.totalItems = calculations.totalItems;
            sale.status = "Completed";
            sale.notes = saleData.notes;

            const savedSale = await queryRunner.manager.save(Sales, sale);

            // 6. Create sale items
            await this.createSaleItems(queryRunner, savedSale, saleData.items, products);

            // 7. Create GST breakdown
            await this.createGstBreakdown(queryRunner, savedSale, calculations.gstBreakdown);

            // 8. Create payment transaction
            await this.createPaymentTransaction(queryRunner, savedSale, saleData);

            // 9. Handle credit transaction if needed
            if (saleData.paymentMethod === "Credit" && customer) {
                await this.createCreditTransaction(queryRunner, customer, savedSale, calculations.grandTotal);
            }

            // 10. Update stock
            await this.updateStock(queryRunner, saleData.items, products, savedSale.id);

            // 11. Update customer statistics
            if (customer) {
                await this.updateCustomerStatistics(queryRunner, customer, calculations.grandTotal);
            }

            // 12. Update daily summary
            await this.updateDailySummary(queryRunner, savedSale);

            await queryRunner.commitTransaction();

            // Return sale with relations
            return await this.salesRepository.findOne({
                where: { id: savedSale.id },
                relations: ["customer", "items", "items.product", "gstBreakdown", "paymentTransactions"]
            }) as Sales;

        } catch (error) {
            await queryRunner.rollbackTransaction();
            throw error;
        } finally {
            await queryRunner.release();
        }
    }

    private async validateProductsAndStock(queryRunner: QueryRunner, items: CreateSaleRequest['items']): Promise<Product[]> {
        const products: Product[] = [];
        
        for (const item of items) {
            const product = await queryRunner.manager.findOne(Product, {
                where: { id: item.productId },
                relations: ["category", "unit", "supplier"]
            });

            if (!product) {
                throw new Error(`Product with ID ${item.productId} not found`);
            }

            if (product.stock < item.quantity) {
                throw new Error(`Insufficient stock for ${product.name}. Available: ${product.stock}, Required: ${item.quantity}`);
            }

            products.push(product);
        }

        return products;
    }

    private calculateSaleTotals(
        items: CreateSaleRequest['items'], 
        discountAmount: number = 0, 
        discountPercentage: number = 0
    ) {
        let subtotal = 0;
        let totalGst = 0;
        let totalItems = 0;
        const gstBreakdown: Map<number, { taxableAmount: number; gstAmount: number }> = new Map();

        // Calculate item totals
        for (const item of items) {
            const itemTotal = item.quantity * item.unitPrice;
            const itemDiscount = item.discountAmount || 0;
            const taxableAmount = itemTotal - itemDiscount;
            const gstAmount = (taxableAmount * item.gstRate) / 100;

            subtotal += itemTotal;
            totalGst += gstAmount;
            totalItems += item.quantity;

            // Group by GST rate for breakdown
            const existing = gstBreakdown.get(item.gstRate) || { taxableAmount: 0, gstAmount: 0 };
            gstBreakdown.set(item.gstRate, {
                taxableAmount: existing.taxableAmount + taxableAmount,
                gstAmount: existing.gstAmount + gstAmount
            });
        }

        // Apply overall discount
        let finalDiscountAmount = discountAmount;
        if (discountPercentage > 0) {
            finalDiscountAmount = (subtotal * discountPercentage) / 100;
        }

        const grandTotal = subtotal + totalGst - finalDiscountAmount;

        return {
            subtotal,
            totalGst,
            discountAmount: finalDiscountAmount,
            discountPercentage,
            grandTotal,
            totalItems,
            gstBreakdown: Array.from(gstBreakdown.entries()).map(([rate, data]) => ({
                rate,
                taxableAmount: data.taxableAmount,
                gstAmount: data.gstAmount
            }))
        };
    }

    private async generateInvoiceNumber(queryRunner: QueryRunner): Promise<string> {
        const sequence = await queryRunner.manager.findOne(InvoiceSequence, {
            where: { sequenceType: "Invoice" }
        });

        if (!sequence) {
            throw new Error("Invoice sequence not configured");
        }

        const invoiceNumber = sequence.generateInvoiceNumber();
        sequence.incrementNumber();
        await queryRunner.manager.save(InvoiceSequence, sequence);

        return invoiceNumber;
    }

    private async generateBillNumber(queryRunner: QueryRunner): Promise<string> {
        const sequence = await queryRunner.manager.findOne(InvoiceSequence, {
            where: { sequenceType: "Bill" }
        });

        if (!sequence) {
            throw new Error("Bill sequence not configured");
        }

        const billNumber = sequence.generateInvoiceNumber();
        sequence.incrementNumber();
        await queryRunner.manager.save(InvoiceSequence, sequence);

        return billNumber;
    }

    async getSalesHistory(
        page: number = 1,
        limit: number = 20,
        filters?: {
            startDate?: Date;
            endDate?: Date;
            customerId?: number;
            paymentMethod?: string;
            status?: string;
        }
    ): Promise<{ sales: Sales[]; total: number; page: number; totalPages: number }> {
        const queryBuilder = this.salesRepository.createQueryBuilder("sale")
            .leftJoinAndSelect("sale.customer", "customer")
            .leftJoinAndSelect("sale.items", "items")
            .leftJoinAndSelect("items.product", "product")
            .orderBy("sale.createdAt", "DESC");

        // Apply filters
        if (filters?.startDate) {
            queryBuilder.andWhere("sale.saleDate >= :startDate", { startDate: filters.startDate });
        }
        if (filters?.endDate) {
            queryBuilder.andWhere("sale.saleDate <= :endDate", { endDate: filters.endDate });
        }
        if (filters?.customerId) {
            queryBuilder.andWhere("sale.customer.id = :customerId", { customerId: filters.customerId });
        }
        if (filters?.paymentMethod) {
            queryBuilder.andWhere("sale.paymentMethod = :paymentMethod", { paymentMethod: filters.paymentMethod });
        }
        if (filters?.status) {
            queryBuilder.andWhere("sale.status = :status", { status: filters.status });
        }

        const total = await queryBuilder.getCount();
        const sales = await queryBuilder
            .skip((page - 1) * limit)
            .take(limit)
            .getMany();

        return {
            sales,
            total,
            page,
            totalPages: Math.ceil(total / limit)
        };
    }

    async getDailySummary(date?: Date): Promise<SalesSummary> {
        const targetDate = date || new Date();
        const startOfDay = new Date(targetDate);
        startOfDay.setHours(0, 0, 0, 0);
        const endOfDay = new Date(targetDate);
        endOfDay.setHours(23, 59, 59, 999);

        const sales = await this.salesRepository.find({
            where: {
                saleDate: targetDate,
                status: "Completed"
            },
            relations: ["gstBreakdown"]
        });

        const totalSales = sales.reduce((sum, sale) => sum + sale.grandTotal, 0);
        const totalTransactions = sales.length;
        const averageOrderValue = totalTransactions > 0 ? totalSales / totalTransactions : 0;

        // Payment method breakdown
        const paymentBreakdown = new Map<string, number>();
        sales.forEach(sale => {
            const current = paymentBreakdown.get(sale.paymentMethod) || 0;
            paymentBreakdown.set(sale.paymentMethod, current + sale.grandTotal);
        });

        const paymentMethodBreakdown = Array.from(paymentBreakdown.entries()).map(([method, amount]) => ({
            method,
            amount,
            percentage: totalSales > 0 ? (amount / totalSales) * 100 : 0
        }));

        // GST breakdown
        const gstBreakdown = new Map<number, { taxableAmount: number; gstAmount: number }>();
        sales.forEach(sale => {
            sale.gstBreakdown.forEach(gst => {
                const existing = gstBreakdown.get(gst.gstRate) || { taxableAmount: 0, gstAmount: 0 };
                gstBreakdown.set(gst.gstRate, {
                    taxableAmount: existing.taxableAmount + gst.taxableAmount,
                    gstAmount: existing.gstAmount + gst.gstAmount
                });
            });
        });

        const gstBreakdownArray = Array.from(gstBreakdown.entries()).map(([rate, data]) => ({
            rate,
            taxableAmount: data.taxableAmount,
            gstAmount: data.gstAmount
        }));

        return {
            totalSales,
            totalTransactions,
            averageOrderValue,
            paymentMethodBreakdown,
            gstBreakdown: gstBreakdownArray
        };
    }

    private async createSaleItems(
        queryRunner: QueryRunner,
        sale: Sales,
        items: CreateSaleRequest['items'],
        products: Product[]
    ): Promise<void> {
        for (let i = 0; i < items.length; i++) {
            const itemData = items[i];
            const product = products[i];

            const saleItem = new SaleItem();
            saleItem.sale = sale;
            saleItem.product = product;
            saleItem.productName = product.name;
            saleItem.productBarcode = product.barcode;
            saleItem.categoryName = product.category.name;
            saleItem.unitName = product.unit.name;
            saleItem.quantity = itemData.quantity;
            saleItem.unitPrice = itemData.unitPrice;
            saleItem.totalPrice = itemData.quantity * itemData.unitPrice;
            saleItem.gstRate = itemData.gstRate;
            saleItem.discountAmount = itemData.discountAmount || 0;

            const taxableAmount = saleItem.totalPrice - saleItem.discountAmount;
            saleItem.gstAmount = (taxableAmount * itemData.gstRate) / 100;
            saleItem.finalAmount = taxableAmount + saleItem.gstAmount;

            await queryRunner.manager.save(SaleItem, saleItem);
        }
    }

    private async createGstBreakdown(
        queryRunner: QueryRunner,
        sale: Sales,
        gstBreakdown: Array<{ rate: number; taxableAmount: number; gstAmount: number }>
    ): Promise<void> {
        for (const gst of gstBreakdown) {
            const breakdown = new GstBreakdown();
            breakdown.sale = sale;
            breakdown.gstRate = gst.rate;
            breakdown.taxableAmount = gst.taxableAmount;
            breakdown.gstAmount = gst.gstAmount;

            await queryRunner.manager.save(GstBreakdown, breakdown);
        }
    }

    private async createPaymentTransaction(
        queryRunner: QueryRunner,
        sale: Sales,
        saleData: CreateSaleRequest
    ): Promise<void> {
        const payment = new PaymentTransaction();
        payment.sale = sale;
        payment.paymentMethod = saleData.paymentMethod;
        payment.amount = sale.grandTotal;
        payment.transactionId = saleData.paymentDetails?.transactionId;
        payment.upiId = saleData.paymentDetails?.upiId;
        payment.referenceNumber = saleData.paymentDetails?.referenceNumber;
        payment.paymentStatus = "Success";
        payment.paymentDate = new Date();

        await queryRunner.manager.save(PaymentTransaction, payment);
    }

    private async createCreditTransaction(
        queryRunner: QueryRunner,
        customer: Customer,
        sale: Sales,
        amount: number
    ): Promise<void> {
        const creditTransaction = new CreditTransaction();
        creditTransaction.customer = customer;
        creditTransaction.sale = sale;
        creditTransaction.transactionType = "Credit_Given";
        creditTransaction.amount = amount;
        creditTransaction.balanceBefore = customer.currentCredit;
        creditTransaction.balanceAfter = customer.currentCredit + amount;
        creditTransaction.status = "Active";
        creditTransaction.description = `Credit given for sale ${sale.invoiceNumber}`;

        // Set due date (30 days from now by default)
        const dueDate = new Date();
        dueDate.setDate(dueDate.getDate() + 30);
        creditTransaction.dueDate = dueDate;

        await queryRunner.manager.save(CreditTransaction, creditTransaction);

        // Update customer credit balance
        customer.currentCredit += amount;
        await queryRunner.manager.save(Customer, customer);
    }

    private async updateStock(
        queryRunner: QueryRunner,
        items: CreateSaleRequest['items'],
        products: Product[],
        saleId: number
    ): Promise<void> {
        for (let i = 0; i < items.length; i++) {
            const itemData = items[i];
            const product = products[i];

            // Create stock movement record
            const stockMovement = StockMovement.createSaleMovement(
                product,
                product.stock,
                itemData.quantity,
                saleId,
                itemData.unitPrice
            );

            await queryRunner.manager.save(StockMovement, stockMovement);

            // Update product stock
            product.stock -= itemData.quantity;

            // Update stock status
            if (product.stock <= 0) {
                product.status = "out";
            } else if (product.stock <= product.minStock) {
                product.status = "low";
            } else {
                product.status = "in";
            }

            await queryRunner.manager.save(Product, product);
        }
    }

    private async updateCustomerStatistics(
        queryRunner: QueryRunner,
        customer: Customer,
        saleAmount: number
    ): Promise<void> {
        customer.totalPurchases += saleAmount;
        customer.totalTransactions += 1;
        customer.lastPurchaseDate = new Date();

        await queryRunner.manager.save(Customer, customer);
    }

    private async updateDailySummary(queryRunner: QueryRunner, sale: Sales): Promise<void> {
        const summaryDate = sale.saleDate;

        let summary = await queryRunner.manager.findOne(DailySalesSummary, {
            where: { summaryDate }
        });

        if (!summary) {
            summary = new DailySalesSummary();
            summary.summaryDate = summaryDate;
        }

        // Update totals
        summary.totalSales += sale.grandTotal;
        summary.totalTransactions += 1;
        summary.totalGstCollected += sale.totalGst;
        summary.totalItemsSold += sale.totalItems;

        // Update payment method breakdown
        switch (sale.paymentMethod) {
            case "Cash":
                summary.cashSales += sale.grandTotal;
                break;
            case "UPI":
                summary.upiSales += sale.grandTotal;
                break;
            case "Credit":
                summary.creditSales += sale.grandTotal;
                break;
            case "Card":
                summary.cardSales += sale.grandTotal;
                break;
        }

        // Recalculate average order value
        summary.averageOrderValue = summary.totalTransactions > 0
            ? summary.totalSales / summary.totalTransactions
            : 0;

        await queryRunner.manager.save(DailySalesSummary, summary);
    }

    async getSaleById(id: number): Promise<Sales | null> {
        return await this.salesRepository.findOne({
            where: { id },
            relations: [
                "customer",
                "items",
                "items.product",
                "items.product.category",
                "items.product.unit",
                "gstBreakdown",
                "paymentTransactions",
                "creditTransactions"
            ]
        });
    }

    async cancelSale(id: number, reason: string): Promise<Sales> {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();

        try {
            const sale = await queryRunner.manager.findOne(Sales, {
                where: { id },
                relations: ["items", "items.product", "customer"]
            });

            if (!sale) {
                throw new Error("Sale not found");
            }

            if (sale.status === "Cancelled") {
                throw new Error("Sale is already cancelled");
            }

            // Restore stock
            for (const item of sale.items) {
                const product = item.product;
                product.stock += item.quantity;

                // Update stock status
                if (product.stock > product.minStock) {
                    product.status = "in";
                } else if (product.stock > 0) {
                    product.status = "low";
                }

                await queryRunner.manager.save(Product, product);

                // Create reverse stock movement
                const stockMovement = StockMovement.createAdjustmentMovement(
                    product,
                    product.stock - item.quantity,
                    item.quantity,
                    `Sale cancellation: ${reason}`
                );

                await queryRunner.manager.save(StockMovement, stockMovement);
            }

            // Handle credit reversal if needed
            if (sale.paymentMethod === "Credit" && sale.customer) {
                const creditTransaction = new CreditTransaction();
                creditTransaction.customer = sale.customer;
                creditTransaction.sale = sale;
                creditTransaction.transactionType = "Adjustment";
                creditTransaction.amount = sale.grandTotal;
                creditTransaction.balanceBefore = sale.customer.currentCredit;
                creditTransaction.balanceAfter = sale.customer.currentCredit - sale.grandTotal;
                creditTransaction.status = "Active";
                creditTransaction.description = `Credit reversal for cancelled sale ${sale.invoiceNumber}: ${reason}`;

                await queryRunner.manager.save(CreditTransaction, creditTransaction);

                // Update customer credit balance
                sale.customer.currentCredit -= sale.grandTotal;
                sale.customer.totalPurchases -= sale.grandTotal;
                sale.customer.totalTransactions -= 1;
                await queryRunner.manager.save(Customer, sale.customer);
            }

            // Update sale status
            sale.status = "Cancelled";
            sale.notes = (sale.notes || "") + `\nCancelled: ${reason}`;
            await queryRunner.manager.save(Sales, sale);

            await queryRunner.commitTransaction();
            return sale;

        } catch (error) {
            await queryRunner.rollbackTransaction();
            throw error;
        } finally {
            await queryRunner.release();
        }
    }
}
