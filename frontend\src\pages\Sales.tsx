import { useNavigate } from "react-router-dom"
import {
    ShoppingCart,
    Plus,
    TrendingUp,
    Users,
    IndianRupee,
    FileText,
    Eye,
    BarChart3,
    Clock,
    CheckCircle,
    AlertTriangle,
    Calendar
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { MetricCard } from "@/components/dashboard/MetricCard"
import { QuickActionCard } from "@/components/dashboard/QuickActionCard"
import { todaysSales, getTodaysSalesTotal, getTodaysSalesCount } from "@/data/sampleTransactions"
import { sampleProducts } from "@/data/sampleProducts"
import { sampleCustomers } from "@/data/sampleCustomers"

export default function Sales() {
    const navigate = useNavigate()

    // Calculate sales metrics
    const todaysSalesTotal = getTodaysSalesTotal()
    const todaysSalesCount = getTodaysSalesCount()
    const averageOrderValue = todaysSalesCount > 0 ? todaysSalesTotal / todaysSalesCount : 0

    // Calculate top-selling products from today's sales
    const productSales = new Map()
    todaysSales.forEach(sale => {
        sale.items.forEach(item => {
            const current = productSales.get(item.productId) || { name: item.productName, quantity: 0, revenue: 0 }
            current.quantity += item.quantity
            current.revenue += item.total
            productSales.set(item.productId, current)
        })
    })
    const topProducts = Array.from(productSales.entries())
        .map(([id, data]) => ({ id, ...data }))
        .sort((a, b) => b.revenue - a.revenue)
        .slice(0, 5)

    // Recent sales for display
    const recentSales = todaysSales.slice(0, 5)

    // Quick actions for sales
    const quickActions = [
        {
            title: "New Sale/Bill",
            description: "Create new bill",
            icon: Plus,
            color: "blue" as const,
            onClick: () => navigate("/sales/new")
        },
        {
            title: "Sales History",
            description: "View all sales",
            icon: FileText,
            color: "green" as const,
            onClick: () => navigate("/sales/history")
        },
        {
            title: "Daily Summary",
            description: "Today's overview",
            icon: Eye,
            color: "purple" as const,
            onClick: () => navigate("/sales/daily")
        },
        {
            title: "Sales Reports",
            description: "Analytics & reports",
            icon: BarChart3,
            color: "orange" as const,
            onClick: () => navigate("/sales/reports")
        }
    ]

    return (
        <div className="flex flex-1 flex-col gap-4 sm:gap-6 p-4 sm:p-6">
            {/* Header */}
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                <div>
                    <h1 className="text-2xl sm:text-3xl font-bold">Sales & Billing</h1>
                    <p className="text-muted-foreground">
                        Manage sales, create bills, and track revenue
                    </p>
                </div>
                <div className="flex items-center gap-2">
                    <Badge variant="outline" className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        Today: {new Date().toLocaleDateString('en-IN')}
                    </Badge>
                </div>
            </div>

            {/* Key Metrics */}
            <div className="grid gap-3 sm:gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
                <MetricCard
                    title="Today's Sales"
                    value={`₹${todaysSalesTotal.toLocaleString('en-IN')}`}
                    icon={IndianRupee}
                    trend={{ direction: "up", percentage: 12, label: "vs yesterday" }}
                />
                <MetricCard
                    title="Bills Created"
                    value={todaysSalesCount.toString()}
                    icon={FileText}
                    trend={{ direction: "up", percentage: 8, label: "vs yesterday" }}
                />
                <MetricCard
                    title="Average Order"
                    value={`₹${averageOrderValue.toLocaleString('en-IN')}`}
                    icon={TrendingUp}
                    trend={{ direction: "up", percentage: 5, label: "vs yesterday" }}
                />
                <MetricCard
                    title="Active Customers"
                    value={sampleCustomers.length.toString()}
                    icon={Users}
                    trend={{ direction: "up", percentage: 3, label: "this month" }}
                />
            </div>

            {/* Quick Actions */}
            <div>
                <h2 className="text-lg font-semibold mb-3">Quick Actions</h2>
                <div className="grid gap-3 sm:gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
                    {quickActions.map((action) => (
                        <QuickActionCard
                            key={action.title}
                            title={action.title}
                            description={action.description}
                            icon={action.icon}
                            color={action.color}
                            onClick={action.onClick}
                        />
                    ))}
                </div>
            </div>

            {/* Main Content Grid */}
            <div className="grid gap-4 sm:gap-6 grid-cols-1 lg:grid-cols-3">
                {/* Recent Sales */}
                <div className="lg:col-span-2">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between">
                            <div>
                                <CardTitle>Recent Sales</CardTitle>
                                <CardDescription>Latest transactions from today</CardDescription>
                            </div>
                            <Button variant="outline" size="sm" onClick={() => navigate("/sales/history")}>
                                View All
                            </Button>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {recentSales.length > 0 ? (
                                    recentSales.map((sale) => (
                                        <div
                                            key={sale.id}
                                            className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 cursor-pointer transition-colors"
                                            onClick={() => navigate(`/sales/details/${sale.id}`)}
                                        >
                                            <div className="flex items-center gap-4">
                                                <div className="p-2 bg-green-100 rounded-lg">
                                                    <ShoppingCart className="h-4 w-4 text-green-600" />
                                                </div>
                                                <div>
                                                    <p className="font-medium">{sale.billNumber}</p>
                                                    <p className="text-sm text-muted-foreground">
                                                        {sale.customerName || "Walk-in Customer"}
                                                    </p>
                                                    <p className="text-xs text-muted-foreground">
                                                        {new Date(sale.timestamp).toLocaleTimeString('en-IN', {
                                                            hour: '2-digit',
                                                            minute: '2-digit'
                                                        })}
                                                    </p>
                                                </div>
                                            </div>
                                            <div className="text-right">
                                                <p className="font-semibold">₹{sale.total.toLocaleString('en-IN')}</p>
                                                <div className="flex items-center gap-2">
                                                    <Badge
                                                        variant={sale.status === 'Completed' ? 'default' : 'secondary'}
                                                        className="text-xs"
                                                    >
                                                        {sale.status === 'Completed' && <CheckCircle className="h-3 w-3 mr-1" />}
                                                        {sale.status === 'Pending' && <Clock className="h-3 w-3 mr-1" />}
                                                        {sale.status}
                                                    </Badge>
                                                    <Badge variant="outline" className="text-xs">
                                                        {sale.paymentMethod}
                                                    </Badge>
                                                </div>
                                            </div>
                                        </div>
                                    ))
                                ) : (
                                    <div className="text-center py-8 text-muted-foreground">
                                        <ShoppingCart className="h-12 w-12 mx-auto mb-4 opacity-50" />
                                        <p>No sales recorded today</p>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            className="mt-2"
                                            onClick={() => navigate("/sales/new")}
                                        >
                                            Create First Sale
                                        </Button>
                                    </div>
                                )}
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Right Sidebar */}
                <div className="space-y-4 sm:space-y-6">
                    {/* Top Products */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Top Products Today</CardTitle>
                            <CardDescription>Best selling items</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                {topProducts.length > 0 ? (
                                    topProducts.map((product, index) => (
                                        <div key={product.id} className="flex items-center justify-between">
                                            <div className="flex items-center gap-3">
                                                <div className="flex items-center justify-center w-6 h-6 bg-blue-100 text-blue-600 rounded-full text-xs font-medium">
                                                    {index + 1}
                                                </div>
                                                <div>
                                                    <p className="font-medium text-sm">{product.name}</p>
                                                    <p className="text-xs text-muted-foreground">
                                                        {product.quantity} units sold
                                                    </p>
                                                </div>
                                            </div>
                                            <p className="font-semibold text-sm">₹{product.revenue.toLocaleString('en-IN')}</p>
                                        </div>
                                    ))
                                ) : (
                                    <p className="text-center text-muted-foreground text-sm py-4">
                                        No sales data available
                                    </p>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Sales Summary */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Today's Summary</CardTitle>
                            <CardDescription>Key performance indicators</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-2">
                                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                        <span className="text-sm">Cash Sales</span>
                                    </div>
                                    <span className="font-medium">
                                        ₹{todaysSales
                                            .filter(sale => sale.paymentMethod === 'Cash')
                                            .reduce((sum, sale) => sum + sale.total, 0)
                                            .toLocaleString('en-IN')}
                                    </span>
                                </div>
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-2">
                                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                        <span className="text-sm">UPI Sales</span>
                                    </div>
                                    <span className="font-medium">
                                        ₹{todaysSales
                                            .filter(sale => sale.paymentMethod === 'UPI')
                                            .reduce((sum, sale) => sum + sale.total, 0)
                                            .toLocaleString('en-IN')}
                                    </span>
                                </div>
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-2">
                                        <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                                        <span className="text-sm">Credit Sales</span>
                                    </div>
                                    <span className="font-medium">
                                        ₹{todaysSales
                                            .filter(sale => sale.paymentMethod === 'Credit')
                                            .reduce((sum, sale) => sum + sale.total, 0)
                                            .toLocaleString('en-IN')}
                                    </span>
                                </div>
                                <div className="border-t pt-3">
                                    <div className="flex items-center justify-between">
                                        <span className="text-sm font-medium">Total GST Collected</span>
                                        <span className="font-semibold">
                                            ₹{todaysSales
                                                .reduce((sum, sale) => sum + sale.gstAmount, 0)
                                                .toLocaleString('en-IN')}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Quick Tips */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <AlertTriangle className="h-4 w-4 text-orange-500" />
                                Sales Tips
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3 text-sm">
                                <div className="p-3 bg-blue-50 rounded-lg">
                                    <p className="font-medium text-blue-900">💡 Peak Hours</p>
                                    <p className="text-blue-700">Most sales happen between 6-8 PM. Keep popular items stocked!</p>
                                </div>
                                <div className="p-3 bg-green-50 rounded-lg">
                                    <p className="font-medium text-green-900">📱 Digital Payments</p>
                                    <p className="text-green-700">Encourage UPI payments for faster transactions and better tracking.</p>
                                </div>
                                <div className="p-3 bg-orange-50 rounded-lg">
                                    <p className="font-medium text-orange-900">🎯 Upselling</p>
                                    <p className="text-orange-700">Suggest complementary items to increase average order value.</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </div>
    )
}
