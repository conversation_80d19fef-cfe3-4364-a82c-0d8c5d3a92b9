import { useState } from "react"
import { useNavigate } from "react-router-dom"
import {
    ShoppingCart,
    Plus,
    Minus,
    Search,
    Scan,
    User,
    Calculator,
    Printer,
    Save,
    X,
    IndianRupee,
    Package,
    AlertCircle,
    CheckCircle
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { sampleProducts } from "@/data/sampleProducts"
import { sampleCustomers } from "@/data/sampleCustomers"
import type { Product } from "@/data/sampleProducts"
import type { Customer } from "@/data/sampleCustomers"
import BarcodeScanner from "@/components/sales/BarcodeScanner"
import InvoicePreview from "@/components/sales/InvoicePreview"

interface BillItem {
    id: string
    name: string
    product: Product
    quantity: number
    price: number
    total: number
    gstRate: number
    gstAmount: number
    unit: string
    category: string
}

interface BillSummary {
    subtotal: number
    totalGst: number
    grandTotal: number
    itemCount: number
    gstBreakdown: {
        rate: number
        taxableAmount: number
        gstAmount: number
    }[]
}

export default function NewSale() {
    const navigate = useNavigate()
    
    // State management
    const [billItems, setBillItems] = useState<BillItem[]>([])
    const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null)
    const [productSearch, setProductSearch] = useState("")
    const [customerSearch, setCustomerSearch] = useState("")
    const [paymentMethod, setPaymentMethod] = useState<"Cash" | "UPI" | "Credit">("Cash")
    const [showCustomerSelect, setShowCustomerSelect] = useState(false)
    const [showProductSearch, setShowProductSearch] = useState(false)
    const [barcodeInput, setBarcodeInput] = useState("")
    const [isProcessing, setIsProcessing] = useState(false)
    const [showBarcodeScanner, setShowBarcodeScanner] = useState(false)
    const [showInvoicePreview, setShowInvoicePreview] = useState(false)
    const [invoiceNumber, setInvoiceNumber] = useState("")

    // Filter products based on search
    const filteredProducts = sampleProducts.filter(product =>
        product.name.toLowerCase().includes(productSearch.toLowerCase()) ||
        (product.barcode && product.barcode.includes(productSearch))
    )

    // Filter customers based on search
    const filteredCustomers = sampleCustomers.filter(customer =>
        customer.name.toLowerCase().includes(customerSearch.toLowerCase()) ||
        customer.phone.includes(customerSearch)
    )

    // Calculate bill summary with GST breakdown
    const calculateBillSummary = (): BillSummary => {
        const subtotal = billItems.reduce((sum, item) => sum + item.total, 0)
        const totalGst = billItems.reduce((sum, item) => sum + item.gstAmount, 0)
        const grandTotal = subtotal + totalGst
        const itemCount = billItems.reduce((sum, item) => sum + item.quantity, 0)

        // Calculate GST breakdown by rate
        const gstBreakdownMap = new Map<number, { taxableAmount: number; gstAmount: number }>()

        billItems.forEach(item => {
            const rate = item.gstRate
            const existing = gstBreakdownMap.get(rate) || { taxableAmount: 0, gstAmount: 0 }
            gstBreakdownMap.set(rate, {
                taxableAmount: existing.taxableAmount + item.total,
                gstAmount: existing.gstAmount + item.gstAmount
            })
        })

        const gstBreakdown = Array.from(gstBreakdownMap.entries()).map(([rate, data]) => ({
            rate,
            taxableAmount: data.taxableAmount,
            gstAmount: data.gstAmount
        })).sort((a, b) => a.rate - b.rate)

        return { subtotal, totalGst, grandTotal, itemCount, gstBreakdown }
    }

    const billSummary = calculateBillSummary()

    // Generate invoice number
    const generateInvoiceNumber = (): string => {
        const date = new Date()
        const year = date.getFullYear().toString().slice(-2)
        const month = (date.getMonth() + 1).toString().padStart(2, '0')
        const day = date.getDate().toString().padStart(2, '0')
        const time = Date.now().toString().slice(-6)
        return `KGS${year}${month}${day}${time}`
    }

    // Add product to bill
    const addProductToBill = (product: Product, quantity: number = 1) => {
        const existingItemIndex = billItems.findIndex(item => item.product.id === product.id)
        
        if (existingItemIndex >= 0) {
            // Update existing item
            const updatedItems = [...billItems]
            const newQuantity = updatedItems[existingItemIndex].quantity + quantity
            const price = product.sellingPrice
            const total = price * newQuantity
            const gstAmount = (total * product.gstRate) / 100
            
            updatedItems[existingItemIndex] = {
                ...updatedItems[existingItemIndex],
                quantity: newQuantity,
                total,
                gstAmount
            }
            setBillItems(updatedItems)
        } else {
            // Add new item
            const price = product.sellingPrice
            const total = price * quantity
            const gstAmount = (total * product.gstRate) / 100
            
            const newItem: BillItem = {
                id: product.id,
                name: product.name,
                product,
                quantity,
                price,
                total,
                gstRate: product.gstRate,
                gstAmount,
                unit: product.unit,
                category: product.category
            }
            setBillItems([...billItems, newItem])
        }
        
        setProductSearch("")
        setShowProductSearch(false)
    }

    // Update item quantity
    const updateItemQuantity = (productId: string, newQuantity: number) => {
        if (newQuantity <= 0) {
            removeItem(productId)
            return
        }
        
        const updatedItems = billItems.map(item => {
            if (item.product.id === productId) {
                const total = item.price * newQuantity
                const gstAmount = (total * item.product.gstRate) / 100
                return { ...item, quantity: newQuantity, total, gstAmount }
            }
            return item
        })
        setBillItems(updatedItems)
    }

    // Remove item from bill
    const removeItem = (productId: string) => {
        setBillItems(billItems.filter(item => item.product.id !== productId))
    }

    // Handle barcode scan (from camera or manual input)
    const handleBarcodeScanned = (barcode: string) => {
        const product = sampleProducts.find(p => p.barcode === barcode)
        if (product) {
            addProductToBill(product)
            // Show success feedback
            console.log(`Product added: ${product.name}`)
        } else {
            alert(`Product not found with barcode: ${barcode}`)
        }
    }

    // Handle manual barcode search
    const handleBarcodeSearch = () => {
        if (!barcodeInput.trim()) return
        handleBarcodeScanned(barcodeInput.trim())
        setBarcodeInput("")
    }

    // Process sale
    const processSale = async () => {
        if (billItems.length === 0) {
            alert("Please add items to the bill!")
            return
        }

        if (paymentMethod === "Credit" && !selectedCustomer) {
            alert("Please select a customer for credit sales!")
            return
        }

        // Enhanced credit validation for credit sales
        if (paymentMethod === "Credit" && selectedCustomer) {
            const newCreditTotal = selectedCustomer.currentCredit + billSummary.grandTotal
            if (newCreditTotal > selectedCustomer.creditLimit) {
                const exceeded = newCreditTotal - selectedCustomer.creditLimit
                const confirmSale = confirm(
                    `⚠️ CREDIT LIMIT EXCEEDED ⚠️\n\n` +
                    `Customer: ${selectedCustomer.name}\n` +
                    `Current Credit: ₹${selectedCustomer.currentCredit.toLocaleString('en-IN')}\n` +
                    `Credit Limit: ₹${selectedCustomer.creditLimit.toLocaleString('en-IN')}\n` +
                    `Sale Amount: ₹${billSummary.grandTotal.toLocaleString('en-IN')}\n\n` +
                    `This sale will exceed credit limit by ₹${exceeded.toLocaleString('en-IN')}\n\n` +
                    `Do you want to proceed anyway?`
                )
                if (!confirmSale) {
                    return
                }
            }
        }

        setIsProcessing(true)
        
        // Simulate processing delay
        await new Promise(resolve => setTimeout(resolve, 1500))
        
        // Here you would normally send the bill data to your backend
        const billData = {
            items: billItems,
            customer: selectedCustomer,
            paymentMethod,
            summary: billSummary,
            timestamp: new Date().toISOString()
        }
        
        console.log("Bill processed:", billData)
        
        // Show success and redirect
        alert(`Bill created successfully! Total: ₹${billSummary.grandTotal.toLocaleString('en-IN')}`)
        navigate("/sales")
        
        setIsProcessing(false)
    }

    // Preview invoice before processing
    const previewInvoice = () => {
        if (billItems.length === 0) {
            alert("Please add items to the bill!")
            return
        }

        if (paymentMethod === "Credit" && !selectedCustomer) {
            alert("Please select a customer for credit sales!")
            return
        }

        const newInvoiceNumber = generateInvoiceNumber()
        setInvoiceNumber(newInvoiceNumber)
        setShowInvoicePreview(true)
    }

    // Clear bill
    const clearBill = () => {
        setBillItems([])
        setSelectedCustomer(null)
        setPaymentMethod("Cash")
        setProductSearch("")
        setCustomerSearch("")
        setBarcodeInput("")
    }

    return (
        <div className="flex flex-1 flex-col gap-4 sm:gap-6 p-4 sm:p-6">
            {/* Header */}
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                <div>
                    <h1 className="text-2xl sm:text-3xl font-bold">New Sale / Bill</h1>
                    <p className="text-muted-foreground">
                        Create new bill with barcode scanning and GST calculations
                    </p>
                </div>
                <div className="flex items-center gap-2">
                    <Button variant="outline" onClick={() => navigate("/sales")}>
                        <X className="h-4 w-4 mr-2" />
                        Cancel
                    </Button>
                    <Button variant="outline" onClick={clearBill}>
                        Clear Bill
                    </Button>
                </div>
            </div>

            <div className="grid gap-4 sm:gap-6 grid-cols-1 lg:grid-cols-3">
                {/* Left Column - Product Selection */}
                <div className="lg:col-span-2 space-y-4 sm:space-y-6">
                    {/* Barcode Scanner */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Scan className="h-5 w-5" />
                                Barcode Scanner
                            </CardTitle>
                            <CardDescription>
                                Use camera to scan barcodes or enter manually for quick product addition
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                {/* Camera Scanner Button */}
                                <Button
                                    onClick={() => setShowBarcodeScanner(true)}
                                    className="w-full"
                                    size="lg"
                                >
                                    <Scan className="h-4 w-4 mr-2" />
                                    Open Camera Scanner
                                </Button>

                                {/* Manual Entry */}
                                <div className="flex gap-2">
                                    <Input
                                        placeholder="Or enter barcode manually..."
                                        value={barcodeInput}
                                        onChange={(e) => setBarcodeInput(e.target.value)}
                                        onKeyDown={(e) => e.key === 'Enter' && handleBarcodeSearch()}
                                        className="flex-1"
                                    />
                                    <Button onClick={handleBarcodeSearch} variant="outline">
                                        <Search className="h-4 w-4" />
                                    </Button>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Product Search */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Package className="h-5 w-5" />
                                Product Search
                            </CardTitle>
                            <CardDescription>
                                Search and add products to the bill
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                <div className="flex gap-2">
                                    <Input
                                        placeholder="Search products by name..."
                                        value={productSearch}
                                        onChange={(e) => {
                                            setProductSearch(e.target.value)
                                            setShowProductSearch(e.target.value.length > 0)
                                        }}
                                        className="flex-1"
                                    />
                                    <Button 
                                        variant="outline"
                                        onClick={() => setShowProductSearch(!showProductSearch)}
                                    >
                                        <Search className="h-4 w-4" />
                                    </Button>
                                </div>
                                
                                {showProductSearch && (
                                    <div className="border rounded-lg max-h-60 overflow-y-auto">
                                        {filteredProducts.length > 0 ? (
                                            filteredProducts.slice(0, 10).map((product) => (
                                                <div
                                                    key={product.id}
                                                    className="flex items-center justify-between p-3 hover:bg-muted/50 cursor-pointer border-b last:border-b-0"
                                                    onClick={() => addProductToBill(product)}
                                                >
                                                    <div>
                                                        <p className="font-medium">{product.name}</p>
                                                        <p className="text-sm text-muted-foreground">
                                                            {product.category} • Stock: {product.stock} {product.unit}
                                                        </p>
                                                    </div>
                                                    <div className="text-right">
                                                        <p className="font-semibold">₹{product.sellingPrice}</p>
                                                        <p className="text-xs text-muted-foreground">
                                                            GST: {product.gstRate}%
                                                        </p>
                                                    </div>
                                                </div>
                                            ))
                                        ) : (
                                            <div className="p-4 text-center text-muted-foreground">
                                                No products found
                                            </div>
                                        )}
                                    </div>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Bill Items */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <ShoppingCart className="h-5 w-5" />
                                Bill Items ({billSummary.itemCount} items)
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                {billItems.length > 0 ? (
                                    billItems.map((item) => (
                                        <div
                                            key={item.product.id}
                                            className="flex items-center justify-between p-4 border rounded-lg"
                                        >
                                            <div className="flex-1">
                                                <p className="font-medium">{item.product.name}</p>
                                                <p className="text-sm text-muted-foreground">
                                                    ₹{item.price} × {item.quantity} = ₹{item.total.toFixed(2)}
                                                </p>
                                                <p className="text-xs text-muted-foreground">
                                                    GST ({item.product.gstRate}%): ₹{item.gstAmount.toFixed(2)}
                                                </p>
                                            </div>
                                            <div className="flex items-center gap-2">
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => updateItemQuantity(item.product.id, item.quantity - 1)}
                                                >
                                                    <Minus className="h-3 w-3" />
                                                </Button>
                                                <span className="w-8 text-center font-medium">{item.quantity}</span>
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => updateItemQuantity(item.product.id, item.quantity + 1)}
                                                >
                                                    <Plus className="h-3 w-3" />
                                                </Button>
                                                <Button
                                                    variant="destructive"
                                                    size="sm"
                                                    onClick={() => removeItem(item.product.id)}
                                                >
                                                    <X className="h-3 w-3" />
                                                </Button>
                                            </div>
                                        </div>
                                    ))
                                ) : (
                                    <div className="text-center py-8 text-muted-foreground">
                                        <ShoppingCart className="h-12 w-12 mx-auto mb-4 opacity-50" />
                                        <p>No items added to bill</p>
                                        <p className="text-sm">Search and add products above</p>
                                    </div>
                                )}
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Right Column - Customer & Payment */}
                <div className="space-y-4 sm:space-y-6">
                    {/* Customer Selection */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <User className="h-5 w-5" />
                                Customer Selection
                            </CardTitle>
                            <CardDescription>
                                Select customer for credit sales and purchase history
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {selectedCustomer ? (
                                    <div className="space-y-3">
                                        {/* Selected Customer Display */}
                                        <div className="p-4 border rounded-lg bg-gradient-to-r from-green-50 to-blue-50">
                                            <div className="flex items-start justify-between">
                                                <div className="flex-1">
                                                    <div className="flex items-center gap-2 mb-2">
                                                        <p className="font-semibold text-lg">{selectedCustomer.name}</p>
                                                        <Badge variant={selectedCustomer.category === "VIP" ? "default" : selectedCustomer.category === "Wholesale" ? "secondary" : "outline"}>
                                                            {selectedCustomer.category}
                                                        </Badge>
                                                    </div>
                                                    <p className="text-sm text-muted-foreground mb-1">📞 {selectedCustomer.phone}</p>
                                                    <p className="text-sm text-muted-foreground mb-3">📍 {selectedCustomer.address}</p>

                                                    {/* Credit Information */}
                                                    <div className="grid grid-cols-2 gap-3 text-sm">
                                                        <div className="p-2 bg-white rounded border">
                                                            <p className="text-xs text-muted-foreground">Current Credit</p>
                                                            <p className="font-semibold text-orange-600">
                                                                ₹{selectedCustomer.currentCredit.toLocaleString('en-IN')}
                                                            </p>
                                                        </div>
                                                        <div className="p-2 bg-white rounded border">
                                                            <p className="text-xs text-muted-foreground">Credit Limit</p>
                                                            <p className="font-semibold text-green-600">
                                                                ₹{selectedCustomer.creditLimit.toLocaleString('en-IN')}
                                                            </p>
                                                        </div>
                                                        <div className="p-2 bg-white rounded border">
                                                            <p className="text-xs text-muted-foreground">Available Credit</p>
                                                            <p className="font-semibold text-blue-600">
                                                                ₹{(selectedCustomer.creditLimit - selectedCustomer.currentCredit).toLocaleString('en-IN')}
                                                            </p>
                                                        </div>
                                                        <div className="p-2 bg-white rounded border">
                                                            <p className="text-xs text-muted-foreground">Total Purchases</p>
                                                            <p className="font-semibold">
                                                                ₹{selectedCustomer.totalPurchases.toLocaleString('en-IN')}
                                                            </p>
                                                        </div>
                                                    </div>

                                                    {/* Credit Status Warning */}
                                                    {paymentMethod === "Credit" && (
                                                        <div className="mt-3">
                                                            {selectedCustomer.currentCredit + billSummary.grandTotal > selectedCustomer.creditLimit ? (
                                                                <div className="p-2 bg-red-50 border border-red-200 rounded-lg">
                                                                    <div className="flex items-center gap-2 text-red-700">
                                                                        <AlertCircle className="h-4 w-4" />
                                                                        <p className="text-sm font-medium">Credit Limit Exceeded!</p>
                                                                    </div>
                                                                    <p className="text-xs text-red-600 mt-1">
                                                                        This sale will exceed credit limit by ₹{((selectedCustomer.currentCredit + billSummary.grandTotal) - selectedCustomer.creditLimit).toLocaleString('en-IN')}
                                                                    </p>
                                                                </div>
                                                            ) : (
                                                                <div className="p-2 bg-green-50 border border-green-200 rounded-lg">
                                                                    <div className="flex items-center gap-2 text-green-700">
                                                                        <CheckCircle className="h-4 w-4" />
                                                                        <p className="text-sm font-medium">Credit Available</p>
                                                                    </div>
                                                                    <p className="text-xs text-green-600 mt-1">
                                                                        Remaining credit after this sale: ₹{(selectedCustomer.creditLimit - selectedCustomer.currentCredit - billSummary.grandTotal).toLocaleString('en-IN')}
                                                                    </p>
                                                                </div>
                                                            )}
                                                        </div>
                                                    )}
                                                </div>
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => setSelectedCustomer(null)}
                                                >
                                                    <X className="h-3 w-3" />
                                                </Button>
                                            </div>
                                        </div>
                                    </div>
                                ) : (
                                    <div className="space-y-3">
                                        {/* Customer Search */}
                                        <div className="flex gap-2">
                                            <Input
                                                placeholder="Search by name, phone, or address..."
                                                value={customerSearch}
                                                onChange={(e) => {
                                                    setCustomerSearch(e.target.value)
                                                    setShowCustomerSelect(e.target.value.length > 0)
                                                }}
                                                className="flex-1"
                                            />
                                            <Button
                                                variant="outline"
                                                onClick={() => setShowCustomerSelect(!showCustomerSelect)}
                                            >
                                                <Search className="h-4 w-4" />
                                            </Button>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => alert("Add New Customer feature coming soon!")}
                                            >
                                                <Plus className="h-4 w-4" />
                                            </Button>
                                        </div>

                                        {/* Customer Selection Dropdown */}
                                        {showCustomerSelect && (
                                            <div className="border rounded-lg max-h-64 overflow-y-auto bg-white shadow-lg">
                                                {/* Walk-in Customer Option */}
                                                <div
                                                    className="p-3 hover:bg-muted/50 cursor-pointer border-b bg-gray-50"
                                                    onClick={() => {
                                                        setSelectedCustomer(null)
                                                        setShowCustomerSelect(false)
                                                        setCustomerSearch("")
                                                    }}
                                                >
                                                    <div className="flex items-center gap-3">
                                                        <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                                                            <User className="h-5 w-5 text-gray-500" />
                                                        </div>
                                                        <div>
                                                            <p className="font-medium">Walk-in Customer</p>
                                                            <p className="text-sm text-muted-foreground">Cash/UPI payments only</p>
                                                        </div>
                                                    </div>
                                                </div>

                                                {/* Customer List */}
                                                {filteredCustomers.slice(0, 10).map((customer) => {
                                                    const availableCredit = customer.creditLimit - customer.currentCredit
                                                    const creditStatus = customer.currentCredit > customer.creditLimit * 0.8 ? 'high' :
                                                                        customer.currentCredit > customer.creditLimit * 0.5 ? 'medium' : 'low'

                                                    return (
                                                        <div
                                                            key={customer.id}
                                                            className="p-3 hover:bg-muted/50 cursor-pointer border-b last:border-b-0"
                                                            onClick={() => {
                                                                setSelectedCustomer(customer)
                                                                setShowCustomerSelect(false)
                                                                setCustomerSearch("")
                                                            }}
                                                        >
                                                            <div className="flex items-center gap-3">
                                                                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                                                    <span className="text-blue-600 font-semibold text-sm">
                                                                        {customer.name.charAt(0).toUpperCase()}
                                                                    </span>
                                                                </div>
                                                                <div className="flex-1">
                                                                    <div className="flex items-center gap-2">
                                                                        <p className="font-medium">{customer.name}</p>
                                                                        <Badge
                                                                            variant={customer.category === "VIP" ? "default" : customer.category === "Wholesale" ? "secondary" : "outline"}
                                                                            className="text-xs"
                                                                        >
                                                                            {customer.category}
                                                                        </Badge>
                                                                    </div>
                                                                    <p className="text-sm text-muted-foreground">{customer.phone}</p>
                                                                    <div className="flex items-center gap-4 mt-1">
                                                                        <p className="text-xs text-muted-foreground">
                                                                            Credit: ₹{customer.currentCredit.toLocaleString('en-IN')} / ₹{customer.creditLimit.toLocaleString('en-IN')}
                                                                        </p>
                                                                        <Badge
                                                                            variant={creditStatus === 'high' ? 'destructive' : creditStatus === 'medium' ? 'secondary' : 'default'}
                                                                            className="text-xs"
                                                                        >
                                                                            {creditStatus === 'high' ? 'High Usage' : creditStatus === 'medium' ? 'Medium Usage' : 'Available'}
                                                                        </Badge>
                                                                    </div>
                                                                </div>
                                                                <div className="text-right">
                                                                    <p className="text-sm font-medium text-green-600">
                                                                        ₹{availableCredit.toLocaleString('en-IN')}
                                                                    </p>
                                                                    <p className="text-xs text-muted-foreground">Available</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    )
                                                })}

                                                {filteredCustomers.length === 0 && customerSearch && (
                                                    <div className="p-4 text-center text-muted-foreground">
                                                        <User className="h-8 w-8 mx-auto mb-2 opacity-50" />
                                                        <p>No customers found</p>
                                                        <Button
                                                            variant="outline"
                                                            size="sm"
                                                            className="mt-2"
                                                            onClick={() => alert("Add New Customer feature coming soon!")}
                                                        >
                                                            Add "{customerSearch}" as new customer
                                                        </Button>
                                                    </div>
                                                )}
                                            </div>
                                        )}
                                    </div>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Payment Method */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <IndianRupee className="h-5 w-5" />
                                Payment Method
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <Select value={paymentMethod} onValueChange={(value) => setPaymentMethod(value as "Cash" | "UPI" | "Credit")}>
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="Cash">Cash Payment</SelectItem>
                                    <SelectItem value="UPI">UPI Payment</SelectItem>
                                    <SelectItem value="Credit">Credit Sale</SelectItem>
                                </SelectContent>
                            </Select>

                            {paymentMethod === "Credit" && !selectedCustomer && (
                                <div className="mt-2 p-2 bg-orange-50 border border-orange-200 rounded-lg">
                                    <div className="flex items-center gap-2 text-orange-700">
                                        <AlertCircle className="h-4 w-4" />
                                        <p className="text-sm">Please select a customer for credit sales</p>
                                    </div>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Bill Summary */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Calculator className="h-5 w-5" />
                                Bill Summary
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                <div className="flex justify-between">
                                    <span>Subtotal:</span>
                                    <span>₹{billSummary.subtotal.toFixed(2)}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span>Total GST:</span>
                                    <span>₹{billSummary.totalGst.toFixed(2)}</span>
                                </div>
                                <Separator />
                                <div className="flex justify-between text-lg font-semibold">
                                    <span>Grand Total:</span>
                                    <span>₹{billSummary.grandTotal.toFixed(2)}</span>
                                </div>

                                {billSummary.itemCount > 0 && (
                                    <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                                        <p className="text-sm text-blue-700">
                                            <strong>{billSummary.itemCount}</strong> items •
                                            Average: ₹{(billSummary.grandTotal / billSummary.itemCount).toFixed(2)} per item
                                        </p>
                                    </div>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Action Buttons */}
                    <Card>
                        <CardContent className="pt-6">
                            <div className="space-y-3">
                                <Button
                                    className="w-full"
                                    size="lg"
                                    onClick={previewInvoice}
                                    disabled={billItems.length === 0 || (paymentMethod === "Credit" && !selectedCustomer)}
                                >
                                    <Printer className="h-4 w-4 mr-2" />
                                    Preview & Complete Sale - ₹{billSummary.grandTotal.toFixed(2)}
                                </Button>

                                <Button
                                    className="w-full"
                                    variant="outline"
                                    size="lg"
                                    onClick={processSale}
                                    disabled={billItems.length === 0 || isProcessing || (paymentMethod === "Credit" && !selectedCustomer)}
                                >
                                    {isProcessing ? (
                                        <>
                                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                            Processing...
                                        </>
                                    ) : (
                                        <>
                                            <CheckCircle className="h-4 w-4 mr-2" />
                                            Quick Complete Sale
                                        </>
                                    )}
                                </Button>

                                <div className="grid grid-cols-2 gap-2">
                                    <Button variant="outline" size="sm" disabled={billItems.length === 0}>
                                        <Save className="h-4 w-4 mr-2" />
                                        Save Draft
                                    </Button>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        disabled={billItems.length === 0}
                                        onClick={previewInvoice}
                                    >
                                        <Printer className="h-4 w-4 mr-2" />
                                        Invoice Preview
                                    </Button>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Quick Stats */}
                    {billItems.length > 0 && (
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-sm">Quick Stats</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-2 text-sm">
                                    <div className="flex justify-between">
                                        <span>Items:</span>
                                        <span>{billSummary.itemCount}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span>GST Rate:</span>
                                        <span>{((billSummary.totalGst / billSummary.subtotal) * 100).toFixed(1)}%</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span>Payment:</span>
                                        <Badge variant={paymentMethod === "Cash" ? "default" : paymentMethod === "UPI" ? "secondary" : "destructive"}>
                                            {paymentMethod}
                                        </Badge>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    )}
                </div>
            </div>

            {/* Barcode Scanner Modal */}
            <BarcodeScanner
                isOpen={showBarcodeScanner}
                onClose={() => setShowBarcodeScanner(false)}
                onBarcodeScanned={handleBarcodeScanned}
            />

            {/* Invoice Preview Modal */}
            <InvoicePreview
                isOpen={showInvoicePreview}
                onClose={() => setShowInvoicePreview(false)}
                billItems={billItems}
                customer={selectedCustomer}
                paymentMethod={paymentMethod}
                billSummary={billSummary}
                invoiceNumber={invoiceNumber}
                onCompleteSale={() => {
                    setShowInvoicePreview(false)
                    processSale()
                }}
            />
        </div>
    )
}
