import { useState, useEffect } from "react"
import { useNavigate } from "react-router-dom"
import { 
    ShoppingCart, 
    Plus, 
    Minus, 
    Search, 
    Scan, 
    User, 
    Calculator, 
    Printer, 
    Save,
    X,
    IndianRupee,
    Percent,
    Package,
    AlertCircle,
    CheckCircle
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { sampleProducts, Product } from "@/data/sampleProducts"
import { sampleCustomers, Customer } from "@/data/sampleCustomers"
import BarcodeScanner from "@/components/sales/BarcodeScanner"

interface BillItem {
    product: Product
    quantity: number
    price: number
    total: number
    gstAmount: number
}

interface BillSummary {
    subtotal: number
    totalGst: number
    grandTotal: number
    itemCount: number
}

export default function NewSale() {
    const navigate = useNavigate()
    
    // State management
    const [billItems, setBillItems] = useState<BillItem[]>([])
    const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null)
    const [productSearch, setProductSearch] = useState("")
    const [customerSearch, setCustomerSearch] = useState("")
    const [paymentMethod, setPaymentMethod] = useState<"Cash" | "UPI" | "Credit">("Cash")
    const [showCustomerSelect, setShowCustomerSelect] = useState(false)
    const [showProductSearch, setShowProductSearch] = useState(false)
    const [barcodeInput, setBarcodeInput] = useState("")
    const [isProcessing, setIsProcessing] = useState(false)
    const [showBarcodeScanner, setShowBarcodeScanner] = useState(false)

    // Filter products based on search
    const filteredProducts = sampleProducts.filter(product =>
        product.name.toLowerCase().includes(productSearch.toLowerCase()) ||
        product.barcode.includes(productSearch)
    )

    // Filter customers based on search
    const filteredCustomers = sampleCustomers.filter(customer =>
        customer.name.toLowerCase().includes(customerSearch.toLowerCase()) ||
        customer.phone.includes(customerSearch)
    )

    // Calculate bill summary
    const calculateBillSummary = (): BillSummary => {
        const subtotal = billItems.reduce((sum, item) => sum + item.total, 0)
        const totalGst = billItems.reduce((sum, item) => sum + item.gstAmount, 0)
        const grandTotal = subtotal + totalGst
        const itemCount = billItems.reduce((sum, item) => sum + item.quantity, 0)
        
        return { subtotal, totalGst, grandTotal, itemCount }
    }

    const billSummary = calculateBillSummary()

    // Add product to bill
    const addProductToBill = (product: Product, quantity: number = 1) => {
        const existingItemIndex = billItems.findIndex(item => item.product.id === product.id)
        
        if (existingItemIndex >= 0) {
            // Update existing item
            const updatedItems = [...billItems]
            const newQuantity = updatedItems[existingItemIndex].quantity + quantity
            const price = product.sellingPrice
            const total = price * newQuantity
            const gstAmount = (total * product.gstRate) / 100
            
            updatedItems[existingItemIndex] = {
                ...updatedItems[existingItemIndex],
                quantity: newQuantity,
                total,
                gstAmount
            }
            setBillItems(updatedItems)
        } else {
            // Add new item
            const price = product.sellingPrice
            const total = price * quantity
            const gstAmount = (total * product.gstRate) / 100
            
            const newItem: BillItem = {
                product,
                quantity,
                price,
                total,
                gstAmount
            }
            setBillItems([...billItems, newItem])
        }
        
        setProductSearch("")
        setShowProductSearch(false)
    }

    // Update item quantity
    const updateItemQuantity = (productId: string, newQuantity: number) => {
        if (newQuantity <= 0) {
            removeItem(productId)
            return
        }
        
        const updatedItems = billItems.map(item => {
            if (item.product.id === productId) {
                const total = item.price * newQuantity
                const gstAmount = (total * item.product.gstRate) / 100
                return { ...item, quantity: newQuantity, total, gstAmount }
            }
            return item
        })
        setBillItems(updatedItems)
    }

    // Remove item from bill
    const removeItem = (productId: string) => {
        setBillItems(billItems.filter(item => item.product.id !== productId))
    }

    // Handle barcode scan (from camera or manual input)
    const handleBarcodeScanned = (barcode: string) => {
        const product = sampleProducts.find(p => p.barcode === barcode)
        if (product) {
            addProductToBill(product)
            // Show success feedback
            console.log(`Product added: ${product.name}`)
        } else {
            alert(`Product not found with barcode: ${barcode}`)
        }
    }

    // Handle manual barcode search
    const handleBarcodeSearch = () => {
        if (!barcodeInput.trim()) return
        handleBarcodeScanned(barcodeInput.trim())
        setBarcodeInput("")
    }

    // Process sale
    const processSale = async () => {
        if (billItems.length === 0) {
            alert("Please add items to the bill!")
            return
        }

        if (paymentMethod === "Credit" && !selectedCustomer) {
            alert("Please select a customer for credit sales!")
            return
        }

        setIsProcessing(true)
        
        // Simulate processing delay
        await new Promise(resolve => setTimeout(resolve, 1500))
        
        // Here you would normally send the bill data to your backend
        const billData = {
            items: billItems,
            customer: selectedCustomer,
            paymentMethod,
            summary: billSummary,
            timestamp: new Date().toISOString()
        }
        
        console.log("Bill processed:", billData)
        
        // Show success and redirect
        alert(`Bill created successfully! Total: ₹${billSummary.grandTotal.toLocaleString('en-IN')}`)
        navigate("/sales")
        
        setIsProcessing(false)
    }

    // Clear bill
    const clearBill = () => {
        setBillItems([])
        setSelectedCustomer(null)
        setPaymentMethod("Cash")
        setProductSearch("")
        setCustomerSearch("")
        setBarcodeInput("")
    }

    return (
        <div className="flex flex-1 flex-col gap-4 sm:gap-6 p-4 sm:p-6">
            {/* Header */}
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                <div>
                    <h1 className="text-2xl sm:text-3xl font-bold">New Sale / Bill</h1>
                    <p className="text-muted-foreground">
                        Create new bill with barcode scanning and GST calculations
                    </p>
                </div>
                <div className="flex items-center gap-2">
                    <Button variant="outline" onClick={() => navigate("/sales")}>
                        <X className="h-4 w-4 mr-2" />
                        Cancel
                    </Button>
                    <Button variant="outline" onClick={clearBill}>
                        Clear Bill
                    </Button>
                </div>
            </div>

            <div className="grid gap-4 sm:gap-6 grid-cols-1 lg:grid-cols-3">
                {/* Left Column - Product Selection */}
                <div className="lg:col-span-2 space-y-4 sm:space-y-6">
                    {/* Barcode Scanner */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Scan className="h-5 w-5" />
                                Barcode Scanner
                            </CardTitle>
                            <CardDescription>
                                Use camera to scan barcodes or enter manually for quick product addition
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                {/* Camera Scanner Button */}
                                <Button
                                    onClick={() => setShowBarcodeScanner(true)}
                                    className="w-full"
                                    size="lg"
                                >
                                    <Scan className="h-4 w-4 mr-2" />
                                    Open Camera Scanner
                                </Button>

                                {/* Manual Entry */}
                                <div className="flex gap-2">
                                    <Input
                                        placeholder="Or enter barcode manually..."
                                        value={barcodeInput}
                                        onChange={(e) => setBarcodeInput(e.target.value)}
                                        onKeyPress={(e) => e.key === 'Enter' && handleBarcodeSearch()}
                                        className="flex-1"
                                    />
                                    <Button onClick={handleBarcodeSearch} variant="outline">
                                        <Search className="h-4 w-4" />
                                    </Button>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Product Search */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Package className="h-5 w-5" />
                                Product Search
                            </CardTitle>
                            <CardDescription>
                                Search and add products to the bill
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                <div className="flex gap-2">
                                    <Input
                                        placeholder="Search products by name..."
                                        value={productSearch}
                                        onChange={(e) => {
                                            setProductSearch(e.target.value)
                                            setShowProductSearch(e.target.value.length > 0)
                                        }}
                                        className="flex-1"
                                    />
                                    <Button 
                                        variant="outline"
                                        onClick={() => setShowProductSearch(!showProductSearch)}
                                    >
                                        <Search className="h-4 w-4" />
                                    </Button>
                                </div>
                                
                                {showProductSearch && (
                                    <div className="border rounded-lg max-h-60 overflow-y-auto">
                                        {filteredProducts.length > 0 ? (
                                            filteredProducts.slice(0, 10).map((product) => (
                                                <div
                                                    key={product.id}
                                                    className="flex items-center justify-between p-3 hover:bg-muted/50 cursor-pointer border-b last:border-b-0"
                                                    onClick={() => addProductToBill(product)}
                                                >
                                                    <div>
                                                        <p className="font-medium">{product.name}</p>
                                                        <p className="text-sm text-muted-foreground">
                                                            {product.category} • Stock: {product.stock} {product.unit}
                                                        </p>
                                                    </div>
                                                    <div className="text-right">
                                                        <p className="font-semibold">₹{product.sellingPrice}</p>
                                                        <p className="text-xs text-muted-foreground">
                                                            GST: {product.gstRate}%
                                                        </p>
                                                    </div>
                                                </div>
                                            ))
                                        ) : (
                                            <div className="p-4 text-center text-muted-foreground">
                                                No products found
                                            </div>
                                        )}
                                    </div>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Bill Items */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <ShoppingCart className="h-5 w-5" />
                                Bill Items ({billSummary.itemCount} items)
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                {billItems.length > 0 ? (
                                    billItems.map((item) => (
                                        <div
                                            key={item.product.id}
                                            className="flex items-center justify-between p-4 border rounded-lg"
                                        >
                                            <div className="flex-1">
                                                <p className="font-medium">{item.product.name}</p>
                                                <p className="text-sm text-muted-foreground">
                                                    ₹{item.price} × {item.quantity} = ₹{item.total.toFixed(2)}
                                                </p>
                                                <p className="text-xs text-muted-foreground">
                                                    GST ({item.product.gstRate}%): ₹{item.gstAmount.toFixed(2)}
                                                </p>
                                            </div>
                                            <div className="flex items-center gap-2">
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => updateItemQuantity(item.product.id, item.quantity - 1)}
                                                >
                                                    <Minus className="h-3 w-3" />
                                                </Button>
                                                <span className="w-8 text-center font-medium">{item.quantity}</span>
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => updateItemQuantity(item.product.id, item.quantity + 1)}
                                                >
                                                    <Plus className="h-3 w-3" />
                                                </Button>
                                                <Button
                                                    variant="destructive"
                                                    size="sm"
                                                    onClick={() => removeItem(item.product.id)}
                                                >
                                                    <X className="h-3 w-3" />
                                                </Button>
                                            </div>
                                        </div>
                                    ))
                                ) : (
                                    <div className="text-center py-8 text-muted-foreground">
                                        <ShoppingCart className="h-12 w-12 mx-auto mb-4 opacity-50" />
                                        <p>No items added to bill</p>
                                        <p className="text-sm">Search and add products above</p>
                                    </div>
                                )}
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Right Column - Customer & Payment */}
                <div className="space-y-4 sm:space-y-6">
                    {/* Customer Selection */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <User className="h-5 w-5" />
                                Customer
                            </CardTitle>
                            <CardDescription>
                                Select customer (required for credit sales)
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {selectedCustomer ? (
                                    <div className="p-3 border rounded-lg bg-green-50">
                                        <div className="flex items-center justify-between">
                                            <div>
                                                <p className="font-medium">{selectedCustomer.name}</p>
                                                <p className="text-sm text-muted-foreground">{selectedCustomer.phone}</p>
                                                <p className="text-xs text-muted-foreground">
                                                    Credit: ₹{selectedCustomer.currentCredit} / ₹{selectedCustomer.creditLimit}
                                                </p>
                                            </div>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => setSelectedCustomer(null)}
                                            >
                                                <X className="h-3 w-3" />
                                            </Button>
                                        </div>
                                    </div>
                                ) : (
                                    <div className="space-y-2">
                                        <div className="flex gap-2">
                                            <Input
                                                placeholder="Search customers..."
                                                value={customerSearch}
                                                onChange={(e) => {
                                                    setCustomerSearch(e.target.value)
                                                    setShowCustomerSelect(e.target.value.length > 0)
                                                }}
                                                className="flex-1"
                                            />
                                            <Button
                                                variant="outline"
                                                onClick={() => setShowCustomerSelect(!showCustomerSelect)}
                                            >
                                                <Search className="h-4 w-4" />
                                            </Button>
                                        </div>

                                        {showCustomerSelect && (
                                            <div className="border rounded-lg max-h-48 overflow-y-auto">
                                                <div
                                                    className="p-3 hover:bg-muted/50 cursor-pointer border-b"
                                                    onClick={() => {
                                                        setSelectedCustomer(null)
                                                        setShowCustomerSelect(false)
                                                        setCustomerSearch("")
                                                    }}
                                                >
                                                    <p className="font-medium">Walk-in Customer</p>
                                                    <p className="text-sm text-muted-foreground">No customer details</p>
                                                </div>
                                                {filteredCustomers.slice(0, 8).map((customer) => (
                                                    <div
                                                        key={customer.id}
                                                        className="p-3 hover:bg-muted/50 cursor-pointer border-b last:border-b-0"
                                                        onClick={() => {
                                                            setSelectedCustomer(customer)
                                                            setShowCustomerSelect(false)
                                                            setCustomerSearch("")
                                                        }}
                                                    >
                                                        <p className="font-medium">{customer.name}</p>
                                                        <p className="text-sm text-muted-foreground">{customer.phone}</p>
                                                        <p className="text-xs text-muted-foreground">
                                                            Credit: ₹{customer.currentCredit} / ₹{customer.creditLimit}
                                                        </p>
                                                    </div>
                                                ))}
                                            </div>
                                        )}
                                    </div>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Payment Method */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <IndianRupee className="h-5 w-5" />
                                Payment Method
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <Select value={paymentMethod} onValueChange={(value: "Cash" | "UPI" | "Credit") => setPaymentMethod(value)}>
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="Cash">Cash Payment</SelectItem>
                                    <SelectItem value="UPI">UPI Payment</SelectItem>
                                    <SelectItem value="Credit">Credit Sale</SelectItem>
                                </SelectContent>
                            </Select>

                            {paymentMethod === "Credit" && !selectedCustomer && (
                                <div className="mt-2 p-2 bg-orange-50 border border-orange-200 rounded-lg">
                                    <div className="flex items-center gap-2 text-orange-700">
                                        <AlertCircle className="h-4 w-4" />
                                        <p className="text-sm">Please select a customer for credit sales</p>
                                    </div>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Bill Summary */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Calculator className="h-5 w-5" />
                                Bill Summary
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                <div className="flex justify-between">
                                    <span>Subtotal:</span>
                                    <span>₹{billSummary.subtotal.toFixed(2)}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span>Total GST:</span>
                                    <span>₹{billSummary.totalGst.toFixed(2)}</span>
                                </div>
                                <Separator />
                                <div className="flex justify-between text-lg font-semibold">
                                    <span>Grand Total:</span>
                                    <span>₹{billSummary.grandTotal.toFixed(2)}</span>
                                </div>

                                {billSummary.itemCount > 0 && (
                                    <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                                        <p className="text-sm text-blue-700">
                                            <strong>{billSummary.itemCount}</strong> items •
                                            Average: ₹{(billSummary.grandTotal / billSummary.itemCount).toFixed(2)} per item
                                        </p>
                                    </div>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Action Buttons */}
                    <Card>
                        <CardContent className="pt-6">
                            <div className="space-y-3">
                                <Button
                                    className="w-full"
                                    size="lg"
                                    onClick={processSale}
                                    disabled={billItems.length === 0 || isProcessing || (paymentMethod === "Credit" && !selectedCustomer)}
                                >
                                    {isProcessing ? (
                                        <>
                                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                            Processing...
                                        </>
                                    ) : (
                                        <>
                                            <CheckCircle className="h-4 w-4 mr-2" />
                                            Complete Sale - ₹{billSummary.grandTotal.toFixed(2)}
                                        </>
                                    )}
                                </Button>

                                <div className="grid grid-cols-2 gap-2">
                                    <Button variant="outline" size="sm" disabled={billItems.length === 0}>
                                        <Save className="h-4 w-4 mr-2" />
                                        Save Draft
                                    </Button>
                                    <Button variant="outline" size="sm" disabled={billItems.length === 0}>
                                        <Printer className="h-4 w-4 mr-2" />
                                        Print Preview
                                    </Button>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Quick Stats */}
                    {billItems.length > 0 && (
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-sm">Quick Stats</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-2 text-sm">
                                    <div className="flex justify-between">
                                        <span>Items:</span>
                                        <span>{billSummary.itemCount}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span>GST Rate:</span>
                                        <span>{((billSummary.totalGst / billSummary.subtotal) * 100).toFixed(1)}%</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span>Payment:</span>
                                        <Badge variant={paymentMethod === "Cash" ? "default" : paymentMethod === "UPI" ? "secondary" : "destructive"}>
                                            {paymentMethod}
                                        </Badge>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    )}
                </div>
            </div>

            {/* Barcode Scanner Modal */}
            <BarcodeScanner
                isOpen={showBarcodeScanner}
                onClose={() => setShowBarcodeScanner(false)}
                onBarcodeScanned={handleBarcodeScanned}
            />
        </div>
    )
}
