import { LucideIcon } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"

interface QuickActionCardProps {
    title: string
    description: string
    icon: LucideIcon
    color: 'blue' | 'green' | 'orange' | 'purple' | 'red' | 'indigo'
    onClick: () => void
    disabled?: boolean
}

// Simple color mapping for easy understanding
const colorClasses = {
    blue: {
        bg: 'bg-blue-50 hover:bg-blue-100',
        icon: 'text-blue-600',
        border: 'border-blue-200'
    },
    green: {
        bg: 'bg-green-50 hover:bg-green-100',
        icon: 'text-green-600',
        border: 'border-green-200'
    },
    orange: {
        bg: 'bg-orange-50 hover:bg-orange-100',
        icon: 'text-orange-600',
        border: 'border-orange-200'
    },
    purple: {
        bg: 'bg-purple-50 hover:bg-purple-100',
        icon: 'text-purple-600',
        border: 'border-purple-200'
    },
    red: {
        bg: 'bg-red-50 hover:bg-red-100',
        icon: 'text-red-600',
        border: 'border-red-200'
    },
    indigo: {
        bg: 'bg-indigo-50 hover:bg-indigo-100',
        icon: 'text-indigo-600',
        border: 'border-indigo-200'
    }
}

export function QuickActionCard({ 
    title, 
    description, 
    icon: Icon, 
    color, 
    onClick, 
    disabled = false 
}: QuickActionCardProps) {
    const colors = colorClasses[color]

    return (
        <Card 
            className={`cursor-pointer transition-all duration-200 ${colors.bg} ${colors.border} ${
                disabled ? 'opacity-50 cursor-not-allowed' : 'hover:shadow-md'
            }`}
            onClick={disabled ? undefined : onClick}
        >
            <CardContent className="p-6">
                <div className="flex flex-col items-center text-center space-y-3">
                    {/* Icon */}
                    <div className={`p-3 rounded-full bg-white ${colors.border}`}>
                        <Icon className={`h-6 w-6 ${colors.icon}`} />
                    </div>
                    
                    {/* Title */}
                    <h3 className="font-semibold text-gray-900">{title}</h3>
                    
                    {/* Description */}
                    <p className="text-sm text-gray-600 leading-relaxed">
                        {description}
                    </p>
                    
                    {/* Action button */}
                    <Button 
                        variant="outline" 
                        size="sm" 
                        className={`mt-2 ${colors.border} ${colors.icon} hover:bg-white`}
                        disabled={disabled}
                    >
                        {disabled ? 'Coming Soon' : 'Open'}
                    </Button>
                </div>
            </CardContent>
        </Card>
    )
}
