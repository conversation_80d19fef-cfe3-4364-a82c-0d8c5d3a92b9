import { Router } from "express";
import { CustomerController } from "../controllers/CustomerController";

const router = Router();
const customerController = new CustomerController();

// Customer CRUD operations
router.post("/", customerController.createCustomer);
router.get("/", customerController.searchCustomers);
router.get("/statistics", customerController.getCustomerStatistics);
router.get("/overdue", customerController.getOverdueCustomers);
router.get("/quick-search", customerController.quickSearchCustomers);
router.get("/:id", customerController.getCustomerById);
router.put("/:id", customerController.updateCustomer);
router.delete("/:id", customerController.deleteCustomer);

// Credit management
router.post("/credit-payment", customerController.recordCreditPayment);
router.get("/:customerId/credit-history", customerController.getCustomerCreditHistory);
router.get("/:customerId/sales-history", customerController.getCustomerSalesHistory);

export default router;
